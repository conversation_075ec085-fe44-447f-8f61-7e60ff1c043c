<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid语法错误测试工具</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2em;
        }
        
        .content {
            padding: 20px;
        }
        
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .test-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            color: #333;
        }
        
        .test-content {
            padding: 20px;
        }
        
        .error-display {
            background-color: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
        }
        
        .error-title {
            color: #e53e3e;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .error-message {
            color: #c53030;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .success-display {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .success-title {
            color: #38a169;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .diagram-container {
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 20px;
            margin: 10px 0;
            background-color: #fafafa;
            min-height: 200px;
        }
        
        .code-display {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .test-button {
            background-color: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .test-button:hover {
            background-color: #3182ce;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background-color: #48bb78;
        }
        
        .status-error {
            background-color: #f56565;
        }
        
        .status-testing {
            background-color: #ed8936;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Mermaid语法错误检测工具</h1>
            <p>专门用于检测和调试时序图语法问题</p>
        </div>
        
        <div class="content">
            <!-- 测试控制面板 -->
            <div class="test-section">
                <div class="test-header">
                    🎛️ 测试控制面板
                </div>
                <div class="test-content">
                    <button class="test-button" onclick="testAddPhraseSequence()">测试添加词汇时序图</button>
                    <button class="test-button" onclick="testDeletePhraseSequence()">测试删除词汇时序图</button>
                    <button class="test-button" onclick="testAllSequences()">测试所有时序图</button>
                    <button class="test-button" onclick="clearResults()">清空结果</button>
                </div>
            </div>
            
            <!-- 测试结果显示区域 -->
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        // 初始化Mermaid配置
        mermaid.initialize({
            startOnLoad: false,
            theme: 'default',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 100,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });

        // 全局错误捕获
        let errorLog = [];
        
        // 重写console.error来捕获Mermaid错误
        const originalConsoleError = console.error;
        console.error = function(...args) {
            errorLog.push({
                timestamp: new Date().toISOString(),
                message: args.join(' '),
                stack: new Error().stack
            });
            originalConsoleError.apply(console, args);
        };

        // 重写console.warn来捕获警告
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            errorLog.push({
                timestamp: new Date().toISOString(),
                message: '[WARNING] ' + args.join(' '),
                stack: new Error().stack
            });
            originalConsoleWarn.apply(console, args);
        };

        // 捕获未处理的错误
        window.addEventListener('error', function(event) {
            errorLog.push({
                timestamp: new Date().toISOString(),
                message: '[GLOBAL ERROR] ' + event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.toString() : 'Unknown error'
            });
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', function(event) {
            errorLog.push({
                timestamp: new Date().toISOString(),
                message: '[PROMISE REJECTION] ' + event.reason,
                stack: event.reason && event.reason.stack ? event.reason.stack : 'No stack trace'
            });
        });

        // 添加词汇时序图代码（语法修复版本）
        const addPhraseSequenceDiagram = `sequenceDiagram
    participant Frontend as 前端客户端
    participant PhraseController as 词汇控制器
    participant DatabaseConfig as 数据库配置
    participant PydanticValidator as 数据验证器
    participant PhraseService as 词汇服务
    participant TextProcessor as 文本处理器
    participant SQLAlchemyORM as ORM映射器
    participant DatabaseEngine as 数据库引擎
    participant ResponseBuilder as 响应构建器

    %% HTTP请求阶段
    Note over Frontend, ResponseBuilder: 🚀 阶段1 HTTP请求处理与路由匹配
    Frontend->>+PhraseController: 1.1 调用 HTTP POST请求<br/>@router.post("/add")<br/>URL /phrase/add<br/>Headers Content-Type application/json<br/>Body {color 0, text "苹果、香蕉、橙子、"}

    %% FastAPI依赖注入系统
    PhraseController->>+DatabaseConfig: 1.2 调用 依赖注入获取会话<br/>async def add_phrase(db Session = Depends(get_db))
    DatabaseConfig->>DatabaseConfig: 1.3 执行 数据库引擎初始化<br/>engine = create_engine(DATABASE_URL)<br/>connect_args={"check_same_thread" False}
    DatabaseConfig->>DatabaseConfig: 1.4 执行 会话工厂创建<br/>SessionLocal = sessionmaker(<br/>  autocommit=False, autoflush=False, bind=engine)
    DatabaseConfig->>DatabaseConfig: 1.5 执行 生成器函数<br/>def get_db()<br/>  db = SessionLocal()<br/>  try yield db<br/>  finally db.close()
    DatabaseConfig-->>-PhraseController: 返回 SQLAlchemy Session对象

    %% Pydantic数据验证阶段
    Note over PhraseController, PydanticValidator: 🔍 阶段2 数据验证与类型转换
    PhraseController->>+PydanticValidator: 2.1 调用 请求体自动验证<br/>text_info TextInfoColorUpdate
    PydanticValidator->>PydanticValidator: 2.2 处理 字段定义验证<br/>class TextInfoColorUpdate(BaseModel)<br/>  color int = Field(ge=0, le=8)<br/>  text str = Field(max_length=1000)
    PydanticValidator->>PydanticValidator: 2.3 处理 JSON反序列化<br/>JSON → Python对象转换<br/>类型检查与约束验证
    PydanticValidator-->>-PhraseController: 返回 TextInfoColorUpdate(color=0, text="苹果、香蕉、橙子、")

    %% 业务逻辑处理阶段
    Note over PhraseController, TextProcessor: 💼 阶段3 业务逻辑与服务层处理
    PhraseController->>+PhraseService: 3.1 调用 词汇添加业务逻辑<br/>service = PhraseService(db)<br/>await service.add_phrase(text_info.color, text_info.text)

    %% 智能编号处理算法
    alt diff_phrase_list is not empty
        Note over PhraseService, DatabaseEngine: 🎯 阶段4 智能编号算法与数据库操作

        loop for each diff phrase
            PhraseService->>+SQLAlchemyORM: 3.10 调用 查询现有同名词汇<br/>self._db.query(Phrase).filter(Phrase.word == word).all()
            SQLAlchemyORM->>+DatabaseEngine: 3.11 执行 SQL查询语句<br/>SELECT phrase.id, phrase.text_id, phrase.word, phrase.type<br/>FROM phrase WHERE phrase.word = ?<br/>参数 ["橙子"]
            DatabaseEngine-->>-SQLAlchemyORM: 返回 查询结果集<br/>[] (新词汇无现有记录)
            SQLAlchemyORM-->>-PhraseService: 返回 Phrase对象列表<br/>all_phrases = []

            PhraseService->>PhraseService: 3.12 处理 智能编号计算<br/>word = self._extract_word_from_block(key)<br/>count = sum(1 for phrase in all_phrases if phrase.word == word)<br/>type_value = count  # 新词汇type=0

            %% 创建新Phrase对象
            PhraseService->>+SQLAlchemyORM: 3.13 调用 创建新词汇实例<br/>phrase = Phrase(<br/>  id=generate_id(),<br/>  text_id=text_info_id,<br/>  word="橙子",<br/>  type=0<br/>)
            SQLAlchemyORM-->>-PhraseService: 返回 新Phrase对象实例

            %% 数据库插入操作
            PhraseService->>+DatabaseEngine: 3.15 执行 SQL插入语句<br/>INSERT INTO phrase (id, textId, word, type)<br/>VALUES (?, ?, ?, ?)<br/>参数 [456, 123, "橙子", 0]
            DatabaseEngine-->>-PhraseService: 返回 插入操作成功 ✅
        end

        PhraseService-->>PhraseController: 返回 业务逻辑处理结果<br/>{"message" "添加成功",<br/> "text_info" {<br/>   "id" 123, "color" 0,<br/>   "text" "苹果、香蕉、橙子、"<br/> }}
    else no new phrases
        PhraseService-->>PhraseController: 返回 无变化业务结果<br/>{"message" "没有新增词汇",<br/> "text_info" current_text_info}
    end

    %% 响应构建与序列化阶段
    Note over PhraseController, ResponseBuilder: 📤 阶段5 HTTP响应构建与JSON序列化
    PhraseController->>+ResponseBuilder: 4.1 调用 FastAPI自动响应构建<br/>return result  # FastAPI自动处理JSON序列化
    ResponseBuilder->>ResponseBuilder: 4.2 处理 Pydantic响应模型<br/>class TextInfoResponse(BaseModel)<br/>  id int<br/>  color int<br/>  text str
    ResponseBuilder->>ResponseBuilder: 4.3 处理 JSON序列化<br/>json.dumps(result, ensure_ascii=False)<br/>Content-Type application/json charset=utf-8<br/>Status 200 OK
    ResponseBuilder-->>-PhraseController: 返回 JSONResponse对象

    %% HTTP响应返回
    PhraseController-->>-Frontend: 4.6 返回 HTTP 200 OK响应<br/>Headers Content-Type application/json<br/>Body {<br/>  "message" "添加成功",<br/>  "text_info" {<br/>    "id" "123", "color" 0,<br/>    "text" "苹果、香蕉、橙子、"<br/>  }<br/>}
`;

        // 删除词汇时序图代码（语法修复版本）
        const deletePhraseSequenceDiagram = `sequenceDiagram
    participant Frontend as 前端客户端
    participant PhraseController as 词汇控制器
    participant DatabaseConfig as 数据库配置
    participant PydanticValidator as 数据验证器
    participant PhraseService as 词汇服务
    participant TextProcessor as 文本处理器
    participant SQLAlchemyORM as ORM映射器
    participant DatabaseEngine as 数据库引擎
    participant ResponseBuilder as 响应构建器

    %% HTTP请求阶段
    Note over Frontend, ResponseBuilder: 🚀 阶段1 HTTP请求处理
    Frontend->>+PhraseController: 1.1 调用 DELETE /phrase/delete<br/>请求体 {color 0, text "苹果、苹果3、香蕉、"}

    %% 依赖注入和验证
    PhraseController->>+DatabaseConfig: 1.2 调用 获取数据库会话<br/>依赖注入 get_db()
    DatabaseConfig-->>-PhraseController: 返回 数据库会话对象

    PhraseController->>+PydanticValidator: 1.3 调用 数据模型验证<br/>TextInfoColorUpdate.model_validate()
    PydanticValidator-->>-PhraseController: 返回 验证通过的请求对象 ✅

    %% 业务逻辑处理阶段
    Note over PhraseController, TextProcessor: 💼 阶段2 业务逻辑与文本分析
    PhraseController->>+PhraseService: 2.1 调用 词汇删除服务<br/>delete_phrase(color=0, new_text="...")

    %% 获取当前文本
    PhraseService->>+SQLAlchemyORM: 2.2 调用 查询当前文本信息<br/>filter(color==0).first()
    SQLAlchemyORM->>+DatabaseEngine: 2.3 执行 SQL查询<br/>SELECT id, color, text FROM text_info WHERE color = 0
    DatabaseEngine-->>-SQLAlchemyORM: 返回 TextInfo(id=123, text="苹果、苹果2、苹果3、香蕉、")
    SQLAlchemyORM-->>-PhraseService: 返回 旧文本信息对象

    %% 删除策略判断
    Note over PhraseService, DatabaseEngine: 🎯 阶段3 删除策略与模式识别
    PhraseService->>PhraseService: 3.1 处理 模式分析<br/>正则表达式 r'^(.+?)(\d+)$'<br/>匹配结果 word="苹果", digit="2"

    alt with digit suffix
        Note over PhraseService, DatabaseEngine: 🔄 复杂删除与全局重编号

        %% 删除目标记录
        PhraseService->>+SQLAlchemyORM: 3.2 调用 删除目标词汇<br/>word="苹果", type=2, text_id=123
        SQLAlchemyORM->>+DatabaseEngine: 3.3 执行 SQL删除<br/>DELETE FROM phrase WHERE word="苹果" AND type=2 AND text_id=123
        DatabaseEngine-->>-SQLAlchemyORM: 返回 删除成功 ✅ (1行受影响)
        SQLAlchemyORM-->>-PhraseService: 返回 删除操作成功

        %% 全局重编号处理
        loop for each phrase to update
            PhraseService->>PhraseService: 3.8 处理 重编号计算<br/>original_type = 3, new_type = 2<br/>old_word = "苹果3", new_word = "苹果2"

            %% 全局文本替换
            loop for each affected text
                PhraseService->>PhraseService: 3.11 处理 字符串替换<br/>text.replace("苹果3", "苹果2")<br/>维护全局一致性

                PhraseService->>+SQLAlchemyORM: 3.12 调用 更新文本内容<br/>text_info.text = updated_text
                SQLAlchemyORM->>+DatabaseEngine: 3.13 执行 SQL更新<br/>UPDATE text_info SET text = ? WHERE id = ?
                DatabaseEngine-->>-SQLAlchemyORM: 返回 文本更新成功 ✅
                SQLAlchemyORM-->>-PhraseService: 返回 全局同步完成
            end

            %% 更新Phrase的type值
            PhraseService->>+SQLAlchemyORM: 3.14 调用 更新词汇类型<br/>phrase.type = new_type (2)
            SQLAlchemyORM->>+DatabaseEngine: 3.15 执行 SQL更新<br/>UPDATE phrase SET type = 2 WHERE id = phrase_id
            DatabaseEngine-->>-SQLAlchemyORM: 返回 类型更新成功 ✅
            SQLAlchemyORM-->>-PhraseService: 返回 词汇重编号完成
        end

        PhraseService-->>PhraseController: 返回 {"message" "删除成功", "updated_text_infos" [...]}

    else simple deletion
        Note over PhraseService, DatabaseEngine: ⚡ 简单删除流程

        PhraseService->>+SQLAlchemyORM: 调用 简单词汇删除<br/>无需重编号
        SQLAlchemyORM->>+DatabaseEngine: 执行 SQL删除<br/>DELETE FROM phrase WHERE word = ?
        DatabaseEngine-->>-SQLAlchemyORM: 返回 删除完成 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 简单删除成功

        PhraseService-->>PhraseController: 返回 {"message" "删除成功"}
    end

    %% 响应构建阶段
    Note over PhraseController, ResponseBuilder: 📤 阶段4 响应构建与清理
    PhraseController->>+ResponseBuilder: 4.1 调用 构建删除响应<br/>格式化复杂/简单删除结果
    ResponseBuilder->>ResponseBuilder: 4.2 处理 响应类型判断<br/>复杂删除 包含updated_text_infos<br/>简单删除 仅消息
    ResponseBuilder->>ResponseBuilder: 4.3 处理 JSON序列化<br/>Content-Type application/json<br/>Status 200 OK
    ResponseBuilder-->>-PhraseController: 返回 格式化的JSONResponse

    PhraseController-->>-Frontend: 4.5 返回 HTTP 200 OK<br/>删除操作完成
`;

        // 测试函数
        function createTestResult(testName, status, diagram = null, errors = []) {
            const testId = 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const statusClass = status === 'success' ? 'status-success' :
                               status === 'error' ? 'status-error' : 'status-testing';

            const resultHtml = `
                <div class="test-section" id="${testId}">
                    <div class="test-header">
                        <span class="status-indicator ${statusClass}"></span>
                        ${testName} - ${status === 'success' ? '✅ 成功' : status === 'error' ? '❌ 失败' : '🔄 测试中'}
                    </div>
                    <div class="test-content">
                        ${errors.length > 0 ? `
                            <div class="error-display">
                                <div class="error-title">🚨 检测到语法错误:</div>
                                <div class="error-message">${errors.map(e => `[${e.timestamp}] ${e.message}`).join('\n\n')}</div>
                            </div>
                        ` : ''}

                        ${status === 'success' ? `
                            <div class="success-display">
                                <div class="success-title">✅ 语法检查通过</div>
                                <div>时序图语法正确，可以正常渲染</div>
                            </div>
                        ` : ''}

                        ${diagram ? `
                            <div class="diagram-container" id="diagram_${testId}">
                                <div>正在渲染时序图...</div>
                            </div>
                        ` : ''}

                        <div class="code-display">
                            <strong>测试的Mermaid代码:</strong>
                            ${diagram || '无代码'}
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('testResults').insertAdjacentHTML('beforeend', resultHtml);
            return testId;
        }

        async function testMermaidDiagram(diagramCode, testName) {
            // 清空错误日志
            errorLog = [];

            const testId = createTestResult(testName, 'testing', diagramCode);

            try {
                // 创建临时容器来测试渲染
                const tempContainer = document.createElement('div');
                tempContainer.style.display = 'none';
                document.body.appendChild(tempContainer);

                // 尝试渲染图表
                const { svg } = await mermaid.render('temp_' + Date.now(), diagramCode);

                // 如果成功，更新状态
                if (errorLog.length === 0) {
                    updateTestResult(testId, 'success', []);

                    // 在实际容器中渲染
                    const diagramContainer = document.getElementById(`diagram_${testId}`);
                    if (diagramContainer) {
                        diagramContainer.innerHTML = svg;
                    }
                } else {
                    updateTestResult(testId, 'error', errorLog);
                }

                // 清理临时容器
                document.body.removeChild(tempContainer);

            } catch (error) {
                // 捕获渲染错误
                const renderError = {
                    timestamp: new Date().toISOString(),
                    message: `[RENDER ERROR] ${error.message}`,
                    stack: error.stack
                };

                updateTestResult(testId, 'error', [...errorLog, renderError]);
            }
        }

        function updateTestResult(testId, status, errors) {
            const testElement = document.getElementById(testId);
            if (!testElement) return;

            const statusIndicator = testElement.querySelector('.status-indicator');
            const header = testElement.querySelector('.test-header');

            // 更新状态指示器
            statusIndicator.className = `status-indicator ${status === 'success' ? 'status-success' : 'status-error'}`;

            // 更新标题
            const testName = header.textContent.split(' - ')[0];
            header.innerHTML = `
                <span class="status-indicator ${status === 'success' ? 'status-success' : 'status-error'}"></span>
                ${testName} - ${status === 'success' ? '✅ 成功' : '❌ 失败'}
            `;

            // 更新内容
            const content = testElement.querySelector('.test-content');
            if (errors.length > 0) {
                const errorDisplay = content.querySelector('.error-display') || document.createElement('div');
                errorDisplay.className = 'error-display';
                errorDisplay.innerHTML = `
                    <div class="error-title">🚨 检测到语法错误:</div>
                    <div class="error-message">${errors.map(e => `[${e.timestamp}] ${e.message}`).join('\n\n')}</div>
                `;
                if (!content.querySelector('.error-display')) {
                    content.insertBefore(errorDisplay, content.firstChild);
                }
            }

            if (status === 'success') {
                const successDisplay = document.createElement('div');
                successDisplay.className = 'success-display';
                successDisplay.innerHTML = `
                    <div class="success-title">✅ 语法检查通过</div>
                    <div>时序图语法正确，可以正常渲染</div>
                `;
                content.insertBefore(successDisplay, content.firstChild);
            }
        }

        // 按钮事件处理函数
        async function testAddPhraseSequence() {
            await testMermaidDiagram(addPhraseSequenceDiagram, '添加词汇时序图语法测试');
        }

        async function testDeletePhraseSequence() {
            await testMermaidDiagram(deletePhraseSequenceDiagram, '删除词汇时序图语法测试');
        }

        async function testAllSequences() {
            await testAddPhraseSequence();
            setTimeout(async () => {
                await testDeletePhraseSequence();
            }, 1000);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            errorLog = [];
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 Mermaid语法错误检测工具已加载');
            console.log('📊 Mermaid版本:', mermaid.version || '10.6.1');
            console.log('⚙️ 配置信息:', mermaid.getConfig());
        });
    </script>
</body>
</html>
