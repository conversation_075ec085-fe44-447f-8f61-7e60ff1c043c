<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Text Processing API - v4.0八层架构时序图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 40px;
        }
        
        h3 {
            color: #2980b9;
            margin-top: 30px;
        }
        
        .sequence-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            background-color: #fafbfc;
        }
        
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .intro {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .intro h2 {
            color: white;
            border-left: 4px solid white;
        }
        
        .architecture-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .layer-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s;
        }
        
        .layer-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .layer-card h4 {
            color: #495057;
            margin-top: 0;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
        }
        
        .layer-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Cube API</h1>
        


        <!-- 词汇添加时序图 -->
        <div class="sequence-section">
            <h3>词汇添加操作时序图 (add_phrase)</h3>
            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant HTTP as HTTP接口层
    participant Config as 配置管理层
    participant Validation as 数据验证层
    participant Business as 业务逻辑层
    participant Tool as 工具算法层
    participant Model as 模型层
    participant Data as 数据持久层
    participant Response as 响应构建层

    Note over Frontend, Response: 词汇添加完整时序流程 - v4.0架构

    %% 1. HTTP请求阶段
    Frontend->>+HTTP: POST /phrase/add<br/>color 0, text "苹果、香蕉、橙子、葡萄、"
    Note right of HTTP: 路由匹配 @router.post('/add') 

    %% 2. 配置层会话管理    
    HTTP->>+Config: 请求数据库会话<br/>Depends(get_db)
    Config->>Config: 创建会话 SessionLocal()
    Config-->>-HTTP: 返回数据库会话对象

    %% 3. 数据验证阶段
    HTTP->>+Validation: 验证请求数据<br/>TextInfoColorUpdate
    Validation->>+Model: 使用Pydantic Schema验证
    Model-->>-Validation: 验证规则 color(0≤x≤8), text(≤1000)
    Validation-->>-HTTP: 验证通过的Python对象

    %% 4. 业务逻辑处理阶段
    HTTP->>+Business: service.add_phrase(color, text)
    Note right of Business: C1 服务初始化

    %% 4.1 查询当前文本信息
    Business->>+Model: 查询TextInfo模型
    Model->>+Data: SELECT * FROM text_info WHERE color=0
    Data-->>-Model: 返回TextInfo对象
    Model-->>-Business: old_text "苹果、香蕉、橙子、"

    %% 4.2 文本处理算法调用 - 旧文本分析
    Business->>+Tool: 调用文本切割算法<br/>split_text_by_comma(old_text)
    Note right of Tool: T1 旧文本切割算法实现
    Tool-->>-Business: old_blocks ["苹果","香蕉","橙子"]

    %% 4.3 文本处理算法调用 - 新文本分析
    Business->>+Tool: 调用文本切割算法<br/>split_text_by_comma(new_text)
    Note right of Tool: T1 新文本切割算法实现
    Tool-->>-Business: new_blocks ["苹果","香蕉","橙子","葡萄"]<br/>tail_text 空

    %% 4.4 差异检测算法
    Business->>+Tool: 调用差异检测算法<br/>find_different_blocks(new_blocks, old_blocks)
    Note right of Tool: T2 差异检测算法实现
    Tool-->>-Business: diff_phrase_list ["葡萄"]

    %% 4.5 索引映射算法
    Business->>+Tool: 调用索引映射算法<br/>get_block_index_map()    
    Note right of Tool: T3 索引映射算法实现
    Tool-->>-Business: block_index_map 葡萄_0位置为3

    %% 4.6 查询所有现有词汇
    Business->>+Model: 查询所有Phrase
    Model->>+Data: SELECT * FROM phrase WHERE text_id=?
    Data-->>-Model: 返回所有现有词汇列表
    Model-->>-Business: all_phrases 苹果(type=0), 香蕉(type=0), 橙子(type=0)

    %% 4.7 智能编号计算
    Business->>Business: C2 智能编号计算<br/>统计葡萄词汇数量<br/>result count = 0

    %% 4.8 新词汇创建
    Business->>+Model: 创建新Phrase对象
    Model->>Model: 创建Phrase对象<br/>word=葡萄, type=0
    Model-->>-Business: 新Phrase对象创建完成

    %% 4.9 文本块修改逻辑
    Business->>Business: C3 文本块修改逻辑<br/>在位置3添加葡萄<br/>count=0无需添加数字后缀

    %% 4.10 数据持久化操作
    Business->>+Model: 批量保存新词汇
    Model->>+Data: 开始事务 BEGIN
    Data->>Data: INSERT INTO phrase VALUES (?, ?, ?, ?)
    Data-->>-Model: 插入成功
    Model-->>-Business: 新词汇保存完成

    %% 4.11 文本重构与更新
    Business->>Business: C4 最终文本构建<br/>构建完整文本内容

    Business->>+Model: 更新TextInfo文本
    Model->>+Data: UPDATE text_info SET text=? WHERE id=?
    Data->>Data: 提交事务 COMMIT
    Data-->>-Model: 更新成功
    Model-->>-Business: TextInfo更新完成

    %% 5. 响应构建阶段
    Business-->>-HTTP: 返回AddResult对象<br/>message 添加成功, text_info ...

    HTTP->>+Response: 构建添加响应<br/>ResponseBuilder.build_add_response()
    Note right of Response: 响应构建层处理添加操作结果

    %% 5.1 响应类型判断
    Response->>Response: E1 响应类型判断<br/>if diff_phrase_list 成功添加响应<br/>else 无新增响应
    Note right of Response: 根据是否有新增词汇选择响应格式

    %% 5.2 数据格式化处理
    Response->>+Model: 请求数据序列化<br/>使用Pydantic Schema
    Model->>Model: ID转字符串 str(text_info.id)<br/>确保前端兼容性
    Model-->>-Response: 序列化后的TextInfo对象

    %% 5.3 响应结构标准化
    Response->>Response: E2 构建标准响应结构<br/>包含success、message、text_info等字段
    Note right of Response: 统一添加操作响应格式

    %% 5.4 HTTP响应配置
    Response->>Response: E3 HTTP响应配置<br/>Content-Type application/json<br/>Status 200 OK
    Response-->>-HTTP: 完整的JSONResponse对象

    %% 6. 会话清理
    HTTP->>Config: 关闭数据库会话<br/>finally db.close()
    Config->>Config: 会话清理和资源释放

    %% 7. 返回响应
    HTTP-->>-Frontend: HTTP 200 OK<br/>success true, message 添加成功, text_info ...

    Note over Frontend, Response: 词汇添加完成，智能编号系统确保数据一致性
            </div>
        </div>

        <!-- 词汇删除时序图 -->
        <div class="sequence-section">
            <h3>词汇删除操作时序图 (delete_phrase)</h3>
            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant HTTP as HTTP接口层
    participant Config as 配置管理层    
    participant Validation as 数据验证层
    participant Business as 业务逻辑层
    participant Tool as 工具算法层
    participant Model as 模型层
    participant Data as 数据持久层
    participant Response as 响应构建层

    Note over Frontend, Response: 词汇删除完整时序流程 - v4.0架构   

    %% 1. HTTP请求阶段
    Frontend->>+HTTP: DELETE /phrase/delete<br/>color 0, text "苹果、香蕉、橙子、"
    Note right of HTTP: 路由匹配 @router.delete('/delete')

    %% 2. 配置层会话管理
    HTTP->>+Config: 请求数据库会话<br/>Depends(get_db)
    Config->>Config: 创建会话 SessionLocal()
    Config-->>-HTTP: 返回数据库会话对象

    %% 3. 数据验证阶段
    HTTP->>+Validation: 验证请求数据<br/>TextInfoColorUpdate
    Validation->>+Model: 使用Pydantic Schema验证
    Model-->>-Validation: 验证规则 color(0≤x≤8), text(≤1000)
    Validation-->>-HTTP: 验证通过的Python对象

    %% 4. 业务逻辑处理阶段
    HTTP->>+Business: service.delete_phrase(color, text)
    Note right of Business: C1 服务初始化

    %% 4.1 查询当前文本信息
    Business->>+Model: 查询TextInfo模型
    Model->>+Data: SELECT * FROM text_info WHERE color=0
    Data-->>-Model: 返回TextInfo对象
    Model-->>-Business: old_text "苹果、香蕉、橙子、葡萄1、"

    %% 4.2 文本处理算法调用 - 旧文本分析
    Business->>+Tool: 调用文本切割算法<br/>split_text_by_comma(old_text)
    Note right of Tool: T1 旧文本切割算法实现
    Tool-->>-Business: old_blocks ["苹果","香蕉","橙子","葡萄1"]

    %% 4.3 文本处理算法调用 - 新文本分析
    Business->>+Tool: 调用文本切割算法<br/>split_text_by_comma(new_text)
    Note right of Tool: T1 新文本切割算法实现
    Tool-->>-Business: new_blocks ["苹果","香蕉","橙子"]

    %% 4.4 差异检测算法
    Business->>+Tool: 调用差异检测算法<br/>find_different_blocks(old_blocks, new_blocks)
    Note right of Tool: T2 差异检测算法实现
    Tool-->>-Business: diff_phrase_list ["葡萄1"]

    %% 4.5 删除类型判断
    Business->>Business: C2 删除类型判断<br/>检查是否包含数字后缀<br/>result 包含数字后缀，复杂删除

    %% 4.6 删除词汇操作
    Business->>+Model: 删除指定词汇
    Model->>+Data: DELETE FROM phrase WHERE word IN ("葡萄1")
    Data-->>-Model: 删除成功
    Model-->>-Business: 词汇删除完成

    %% 4.7 更新TextInfo
    Business->>+Model: 更新TextInfo文本
    Model->>+Data: UPDATE text_info SET text="苹果、香蕉、橙子、" WHERE color=0
    Data-->>-Model: 更新成功
    Model-->>-Business: TextInfo更新完成

    alt 复杂删除模式（包含数字后缀）
        Note over Business, Data: 智能重编号处理

        %% 4.8 查询需要重编号的词汇
        Business->>+Model: 查询相关词汇进行重编号
        Model->>+Data: SELECT * FROM phrase WHERE word LIKE "葡萄%" ORDER BY type
        Data-->>-Model: 返回相关词汇列表
        Model-->>-Business: related_phrases ["葡萄2", "葡萄3"]

        %% 4.9 重编号算法
        Business->>+Tool: 调用重编号算法<br/>renumber_phrases()
        Note right of Tool: T3 智能重编号算法实现
        Tool->>Tool: 重新分配type值<br/>葡萄2 → 葡萄1<br/>葡萄3 → 葡萄2
        Tool-->>-Business: 重编号映射完成

        %% 4.10 批量更新词汇
        Business->>+Model: 批量更新词汇编号
        Model->>+Data: 开始事务 BEGIN
        Data->>Data: UPDATE phrase SET word="葡萄1", type=1 WHERE id=?
        Data->>Data: UPDATE phrase SET word="葡萄2", type=2 WHERE id=?
        Data-->>-Model: 批量更新成功
        Model-->>-Business: 重编号完成

        %% 4.11 更新相关TextInfo
        Business->>+Model: 查询并更新相关TextInfo
        Model->>+Data: SELECT * FROM text_info WHERE text LIKE "%葡萄%"
        Data->>Data: UPDATE text_info SET text=? WHERE id=?
        Data->>Data: 提交事务 COMMIT
        Data-->>-Model: 相关文本更新成功
        Model-->>-Business: 全局更新完成

        Business-->>HTTP: 返回DeleteResult<br/>message 删除成功, updated_text_infos [...]

    else 简单删除模式（无数字后缀）
        Note over Business: 无需重编号处理

        Business-->>HTTP: 返回DeleteResult<br/>message 删除成功
    end

    %% 5. 响应构建阶段
    HTTP->>+Response: 构建删除响应<br/>ResponseBuilder.build_delete_response()
    Note right of Response: 响应构建层处理删除操作结果

    %% 5.1 响应类型判断
    Response->>Response: E1 响应类型判断<br/>if 复杂删除 返回updated_text_infos<br/>else 返回简单message
    Note right of Response: 根据删除复杂度选择响应格式

    %% 5.2 数据格式化处理
    Response->>+Model: 数据序列化<br/>使用Pydantic Schema
    Model->>Model: ID转字符串处理<br/>确保前端兼容性
    Model-->>-Response: 序列化后的对象列表

    %% 5.3 响应结构构建
    Response->>Response: E2 构建标准响应结构<br/>包含message和可选的updated_text_infos
    Note right of Response: 删除操作响应格式

    %% 5.4 HTTP响应配置
    Response->>Response: E3 HTTP响应配置<br/>Content-Type application/json<br/>Status 200 OK
    Response-->>-HTTP: 完整的JSONResponse对象

    %% 6. 会话清理
    HTTP->>Config: 关闭数据库会话<br/>finally db.close()
    Config->>Config: 会话清理和资源释放

    %% 7. 返回响应
    HTTP-->>Frontend: HTTP 200 OK<br/>message 删除成功, updated_text_infos [...]

    deactivate HTTP

    Note over Frontend, Response: 词汇删除完成，智能重编号系统确保全局数据一致性
            </div>
        </div>

        <!-- 坐标更新时序图 --> 
        <div class="sequence-section">
            <h3>坐标更新操作时序图 (coordinate_update)</h3>
            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant HTTP as HTTP接口层
    participant Config as 配置管理层    
    participant Validation as 数据验证层
    participant Business as 业务逻辑层
    participant Tool as 工具算法层
    participant Model as 模型层
    participant Data as 数据持久层
    participant Response as 响应构建层

    Note over Frontend, Response: 坐标更新完整时序流程 - v4.0架构

    %% 1. HTTP请求阶段
    Frontend->>+HTTP: PUT /coordinate/update<br/>id 1, table_id 1, color 0, position "（15， 25）", voc "苹果"
    Note right of HTTP: 路由匹配 @router.put('/update')

    %% 2. 配置层会话管理
    HTTP->>+Config: 请求数据库会话<br/>Depends(get_db)
    Config->>Config: 创建会话 SessionLocal()
    Config-->>-HTTP: 返回数据库会话对象

    %% 3. 数据验证阶段
    HTTP->>+Validation: 验证请求数据<br/>CoordinateUpdate
    Validation->>+Model: 使用Pydantic Schema验证
    Model-->>-Validation: 验证规则 id(>0), table_id(>0), color(0≤x≤8), position(格式), voc(≤255)
    Validation-->>-HTTP: 验证通过的Python对象

    %% 4. 业务逻辑处理阶段
    HTTP->>+Business: service.update_coordinate(id, coordinate_data)
    Note right of Business: C1 服务初始化

    %% 4.1 查询目标坐标
    Business->>+Model: 查询Coordinate模型
    Model->>+Data: SELECT * FROM coordinate WHERE id=1
    Data-->>-Model: 返回Coordinate对象
    Model-->>-Business: coordinate old_voc "香蕉", current_data

    %% 4.2 保存旧voc值
    Business->>Business: C2 保存变更前状态<br/>old_voc "香蕉"<br/>准备更新检测

    %% 4.3 更新坐标基本信息
    Business->>+Model: 更新坐标基本信息
    Model->>Model: coordinate.table_id = 1<br/>coordinate.color = 0<br/>coordinate.position = "（15， 25）"<br/>coordinate.voc = "苹果"
    Model-->>-Business: 基本信息更新完成

    %% 4.4 计算repeated值
    Business->>Business: C3 repeated值计算逻辑<br/>if voc不为空 计算同voc同color数量<br/>else repeated = 0

    alt voc不为空的情况
        Note over Business, Data: 计算repeated值-统计同词汇同颜色数量

        %% 4.5 查询同voc同color的坐标数量
        Business->>+Model: 查询同voc同color坐标数量
        Model->>+Data: SELECT COUNT(*) FROM coordinate<br/>WHERE table_id=1 AND voc="苹果" AND color=0
        Data-->>-Model: 返回计数结果
        Model-->>-Business: same_voc_count 3

        %% 4.6 设置repeated值
        Business->>+Model: 设置repeated值
        Model->>Model: coordinate.repeated = 3
        Model-->>-Business: repeated值设置完成

    else voc为空的情况
        %% 4.5 设置repeated为0
        Business->>+Model: 设置repeated为0
        Model->>Model: coordinate.repeated = 0
        Model-->>-Business: repeated值设置完成
    end

    %% 4.7 提交当前更新
    Business->>+Model: 提交坐标更新
    Model->>+Data: UPDATE coordinate SET table_id=?, color=?, position=?, voc=?, repeated=? WHERE id=1
    Data-->>-Model: 更新成功
    Model-->>-Business: 坐标更新提交完成

    %% 4.8 检查voc变化处理
    Business->>Business: C4 voc变化检测<br/>if old_voc != ne    w_voc AND old_voc 需要重新编号<br/>检测结果 "香蕉" != "苹果" AND "香蕉"存在

    alt voc发生变化且old_voc不为空
        Note over Business, Data: voc变化处理-重新编号相关坐标

        %% 4.9  
        Business->>+Model: 查询需要重新编号的坐标
        Model->>+Data: SELECT * FROM coordinate<br/>WHERE table_id=1 AND voc="香蕉" AND color=0<br/>ORDER BY repeated ASC
        Data-->>-Model: 返回相关坐标列表
        Model-->>-Business: coordinates_to_update [coord1, coord2]

        %% 4.10 重新编号算法
        Business->>+Tool: 调用重新编号算法<br/>update_repeated_values_after_voc_change()
        Note right of Tool: T1 重新编号算法实现
        Tool->>Tool: for i, coord in enumerate(coordinates)<br/>coord.repeated = i<br/>重新分配编号0,1,2...
        Tool-->>-Business: 重新编号完成

        %% 4.11 批量更新repeated值
        Business->>+Model: 批量更新repeated值
        Model->>+Data: 开始事务 BEGIN
        Data->>Data: UPDATE coordinate SET repeated=0 WHERE id=?
        Data->>Data: UPDATE coordinate SET repeated=1 WHERE id=?
        Data->>Data: 提交事务 COMMIT
        Data-->>-Model: 批量更新成功
        Model-->>-Business: 相关坐标重新编号完成

        Business-->>HTTP: 返回CoordinateUpdateResult<br/>coordinates [updated_coordinate]

    else voc未变化或old_voc为空
        Note over Business: 无需重新编号处理

        Business-->>HTTP: 返回CoordinateUpdateResult<br/>coordinates [updated_coordinate]
    end

    %% 5. 响应构建阶段
    HTTP->>+Response: 构建更新响应<br/>ResponseBuilder.build_coordinate_response()
    Note right of Response: 响应构建层处理坐标更新结果

    %% 5.1 响应数据格式化
    Response->>+Model: 数据序列化<br/>使用Pydantic Schema
    Model->>Model: ID转字符串处理<br/>确保前端兼容性
    Model-->>-Response: 序列化后的Coordinate对象

    %% 5.2 响应结构构建
    Response->>Response: E1 构建标准响应结构<br/>包含coordinates字段
    Note right of Response: 坐标更新操作响应格式

    %% 5.3 HTTP响应配置
    Response->>Response: E2 HTTP响应配置<br/>Content-Type application/json<br/>Status 200 OK
    Response-->>-HTTP: 完整的JSONResponse对象

    %% 6. 会话清理
    HTTP->>Config: 关闭数据库会话<br/>finally db.close()
    Config->>Config: 会话清理和资源释放

    %% 7. 返回响应
    HTTP-->>Frontend: HTTP 200 OK<br/>coordinates [updated_coordinate_with_new_repeated]

    deactivate HTTP

    Note over Frontend, Response: 坐标更新完成，智能repeated计算确保数据一致性
            </div>
        </div>

    </div>
</body>
</html>
