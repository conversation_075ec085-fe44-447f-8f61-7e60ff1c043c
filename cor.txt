（-16， 16）  0
（-15， 16）  1
（-14， 16）  1
（-13， 16）  1
（-12， 16）  1
（-11， 16）  1
（-10， 16）  1
（-9， 16）  1
（-8， 16）  1
（-7， 16）  3
（-6， 16）  3
（-5， 16）  3
（-4， 16）  3
（-3， 16）  3
（-2， 16）  3
（-1， 16）  3
（0， 16）  0
（1， 16）  1
（2， 16）  1
（3， 16）  1
（4， 16）  1
（5， 16）  1
（6， 16）  1
（7， 16）  1
（8， 16）  3
（9， 16）  3
（10， 16）  3
（11， 16）  3
（12， 16）  3
（13， 16）  3
（14， 16）  3
（15， 16）  3
（16， 16）  0

（-16， 15）  4
（-15， 15）  8
（-14， 15）  1
（-13， 15）  8
（-12， 15）  1
（-11， 15）  5
（-10， 15）  1
（-9， 15）  5
（-8， 15）  2
（-7， 15）  6
（-6， 15）  3
（-5， 15）  6
（-4， 15）  3
（-3， 15）  7
（-2， 15）  3
（-1， 15）  7
（0， 15）  4
（1， 15）  8
（2， 15）  1
（3， 15）  8
（4， 15）  1
（5， 15）  5
（6， 15）  1
（7， 15）  5
（8， 15）  2
（9， 15）  6
（10， 15） 3
（11， 15）  6
（12， 15）  3
（13， 15）  7
（14， 15）  3
（15， 15）  7
（16， 15）  4

（-16， 14）  4
（-15， 14）  4
（-14， 14）  8
（-13， 14）  1
（-12， 14）  1
（-11， 14）  1
（-10， 14）  5
（-9， 14）  2
（-8， 14）  2
（-7， 14）  2
（-6， 14）  6
（-5， 14）  3
（-4， 14）  3
（-3， 14）  3
（-2， 14）  7
（-1， 14）  4
（0， 14）  4
（1， 14）  4
（2， 14）  8
（3， 14）  1
（4， 14）  1
（5， 14）  1
（6， 14）  5
（7， 14）  2
（8， 14）  2
（9， 14）  2
（10， 14）  6
（11， 14）  3
（12， 14）  3
（13， 14）  3
（14， 14）  7
（15， 14）  4
（16， 14）  4

（-16， 13）  4
（-15， 13）  8
（-14， 13）  4
（-13， 13）  8
（-12， 13）  1
（-11， 13）  5
（-10， 13）  2
（-9， 13）  5
（-8， 13）  2
（-7， 13）  6
（-6， 13）  2
（-5， 13）  6
（-4， 13）  3
（-3， 13）  7
（-2， 13）  4
（-1， 13）  7
（0， 13）  4
（1， 13）  8
（2， 13）  4
（3， 13）  8
（4， 13）  1
（5， 13）  5
（6， 13）  2
（7， 13）  5
（8， 13）  2
（9， 13）  6
（10， 13）  2
（11， 13）  6
（12， 13）  3
（13， 13）  7
（14， 13）  4
（15， 13）  7
（16， 13）  4

（-16， 12）  4
（-15， 12）  4
（-14， 12）  4
（-13， 12）  4
（-12， 12）  8
（-11， 12）  2
（-10， 12）  2
（-9， 12）  2
（-8， 12）  2
（-7， 12）  2
（-6， 12）  2
（-5， 12）  2
（-4， 12）  5
（-3， 12）  4
（-2， 12）  4
（-1， 12）  4
（0， 12）  4
（1， 12）  4
（2， 12）  4
（3， 12）  4
（4， 12）  6
（5， 12）  2
（6， 12）  2
（7， 12）  2
（8， 12）  2
（9， 12）  2
（10， 12）  2
（11， 12）  2
（12， 12）  7
（13， 12）  4
（14， 12）  4
（15， 12）  4
（16， 12）  4

（-16， 11）  4
（-15， 11）  7
（-14， 11）  4
（-13， 11）  7
（-12， 11）  3
（-11， 11）  6
（-10， 11）  2
（-9， 11）  6
（-8， 11）  2
（-7， 11）  5
（-6， 11）  2
（-5， 11）  5
（-4， 11）  1
（-3， 11）  8
（-2， 11）  4
（-1， 11）  8
（0， 11）  4
（1， 11）  7
（2， 11）  4
（3， 11）  7
（4， 11）  3
（5， 11）  6
（6， 11）  2
（7， 11）  6
（8， 11）  2
（9， 11）  5
（10， 11）  2
（11， 11）  5
（12， 11）  1
（13， 11）  8
（14， 11）  4
（15， 11）  8
（16， 11）  4

（-16， 10）  4
（-15， 10）  4
（-14， 10）  7
（-13， 10）  3
（-12， 10）  3
（-11， 10）  3
（-10， 10）  6
（-9， 10）  2
（-8， 10）  2
（-7， 10）  2
（-6， 10）  5
（-5， 10）  1
（-4， 10）  1
（-3， 10）  1
（-2， 10）  8
（-1， 10）  4
（0， 10）  4
（1， 10）  4
（2， 10）  7
（3， 10）  3
（4， 10）  3
（5， 10）  3
（6， 10）  6
（7， 10）  2
（8， 10）  2
（9， 10）  2
（10， 10）  5
（11， 10）  1
（12， 10）  1
（13， 10）  1
（14， 10）  8
（15， 10）  4
（16， 10）  4

（-16， 9）  4
（-15， 9）  7
（-14， 9）  3
（-13， 9）  7
（-12， 9）  3
（-11， 9）  6
（-10， 9）  3
（-9， 9）  6
（-8， 9）  2
（-7， 9）  5
（-6， 9）  1
（-5， 9）  5
（-4， 9）  1
（-3， 9）  8
（-2， 9）  1
（-1， 9）  8
（0， 9）  4
（1， 9）  7
（2， 9）  3
（3， 9）  7
（4， 9）  3
（5， 9）  6
（6， 9）  3
（7， 9）  6
（8， 9）  2
（9， 9）  5
（10， 9）  1
（11， 9）  5
（12， 9）  1
（13， 9）  8
（14， 9）  1
（15， 9）  8
（16， 9）  4

（-16， 8）  4
（-15， 8）  3
（-14， 8）  3
（-13， 8）  3
（-12， 8）  3
（-11， 8）  3
（-10， 8）  3
（-9， 8）  3
（-8， 8）  0
（-7， 8）  1
（-6， 8）  1
（-5， 8）  1
（-4， 8）  1
（-3， 8）  1
（-2， 8）  1
（-1， 8）  1
（0， 8）  2
（1， 8）  3
（2， 8）  3
（3， 8）  3
（4， 8）  3
（5， 8）  3
（6， 8）  3
（7， 8）  3
（8， 8）  0
（9， 8）  1
（10， 8）  1
（11， 8）  1
（12， 8）  1
（13， 8）  1
（14， 8）  1
（15， 8）  1
（16， 8）  4

（-16， 7）  2
（-15， 7）  6
（-14， 7）  3
（-13， 7）  6
（-12， 7）  3
（-11， 7）  7
（-10， 7）  3
（-9， 7）  7
（-8， 7）  4
（-7， 7）  8
（-6， 7）  1
（-5， 7）  8
（-4， 7）  1
（-3， 7）  5
（-2， 7）  1
（-1， 7）  5
（0， 7）  2
（1， 7）  6
（2， 7）  3
（3， 7）  6
（4， 7）  3
（5， 7）  7
（6， 7）  3
（7， 7）  7
（8， 7）  4
（9， 7）  8
（10， 7）  1
（11， 7）  8
（12， 7）  1
（13， 7）  5
（14， 7）  1
（15， 7）  5
（16， 7）  2

（-16， 6）  2
（-15， 6）  2
（-14， 6）  6
（-13， 6）  3
（-12， 6）  3
（-11， 6）  3
（-10， 6）  7
（-9， 6）  4
（-8， 6）  4
（-7， 6）  4
（-6， 6）  8
（-5， 6）  1
（-4， 6）  1
（-3， 6）  1
（-2， 6）  5
（-1， 6）  2
（0， 6）  2
（1， 6）  2
（2， 6）  6
（3， 6）  3
（4， 6）  3
（5， 6）  3
（6， 6）  7
（7， 6）  4
（8， 6）  4
（9， 6）  4
（10， 6）  8
（11， 6）  1
（12， 6）  1
（13， 6）  1
（14， 6）  5
（15， 6）  2
（16， 6）  2

（-16， 5）  2
（-15， 5）  6
（-14， 5）  2
（-13， 5）  6
（-12， 5）  3
（-11， 5）  7
（-10， 5）  4
（-9， 5）  7
（-8， 5）  4
（-7， 5）  8
（-6， 5）  4
（-5， 5）  8
（-4， 5）  1
（-3， 5）  5
（-2， 5）  2
（-1， 5）  5
（0， 5）  2
（1， 5）  6
（2， 5）  2
（3， 5）  6
（4， 5）  3
（5， 5）  7
（6， 5）  4
（7， 5）  7
（8， 5）  4
（9， 5）  8
（10， 5）  4
（11， 5）  8
（12， 5）  1
（13， 5）  5
（14， 5）  2
（15， 5）  5
（16， 5）  2

（-16， 4）  2
（-15， 4）  2
（-14， 4）  2
（-13， 4）  2
（-12， 4）  7
（-11， 4）  4
（-10， 4）  4
（-9， 4）  4
（-8， 4）  4
（-7， 4）  4
（-6， 4）  4
（-5， 4）  4
（-4， 4）  6
（-3， 4）  2
（-2， 4）  2
（-1， 4）  2
（0， 4）  2
（1， 4）  2
（2， 4）  2
（3， 4）  2
（4， 4）  5
（5， 4）  4
（6， 4）  4
（7， 4）  4
（8， 4）  4
（9， 4）  4
（10， 4）  4
（11， 4）  4
（12， 4）  8
（13， 4）  2
（14， 4）  2
（15， 4）  2
（16， 4）  2

（-16， 3）  2
（-15， 3）  5
（-14， 3）  2
（-13， 3）  5
（-12， 3）  1
（-11， 3）  8
（-10， 3）  4
（-9， 3）  8
（-8， 3）  4
（-7， 3）  7
（-6， 3）  4
（-5， 3）  7
（-4， 3）  3
（-3， 3）  6
（-2， 3）  2
（-1， 3）  6
（0， 3）  2
（1， 3）  5
（2， 3）  2
（3， 3）  5
（4， 3）  1
（5， 3）  8
（6， 3）  4
（7， 3）  8
（8， 3）  4
（9， 3）  7
（10， 3）  4
（11， 3）  7
（12， 3）  3
（13， 3）  6
（14， 3）  2
（15， 3）  6
（16， 3）  2

（-16， 2）  2
（-15， 2）  2
（-14， 2）  5
（-13， 2）  1
（-12， 2）  1
（-11， 2）  1
（-10， 2）  8
（-9， 2）  4
（-8， 2）  4
（-7， 2）  4
（-6， 2）  7
（-5， 2）  3
（-4， 2）  3
（-3， 2）  3
（-2， 2）  6
（-1， 2）  2
（0， 2）  2
（1， 2）  2
（2， 2）  5
（3， 2）  1
（4， 2）  1
（5， 2）  1
（6， 2）  8
（7， 2）  4
（8， 2）  4
（9， 2）  4
（10， 2）  7
（11， 2）  3
（12， 2）  3
（13， 2）  3
（14， 2）  6
（15， 2）  2
（16， 2）  2

（-16， 1）  2
（-15， 1）  5
（-14， 1）  1
（-13， 1）  5
（-12， 1）  1
（-11， 1）  8
（-10， 1）  1
（-9， 1）  8
（-8， 1）  4
（-7， 1）  7
（-6， 1）  3
（-5， 1）  7
（-4， 1）  3
（-3， 1）  6
（-2， 1）  3
（-1， 1）  6
（0， 1）  2
（1， 1）  5
（2， 1）  1
（3， 1）  5
（4， 1）  1
（5， 1）  8
（6， 1）  1
（7， 1）  8
（8， 1）  4
（9， 1）  7
（10， 1）  3
（11， 1）  7
（12， 1）  3
（13， 1）  6
（14， 1）  3
（15， 1）  6
（16， 1）  2

（-16， 0）  0
（-15， 0）  1
（-14， 0）  1
（-13， 0）  1
（-12， 0）  1
（-11， 0）  1
（-10， 0）  1
（-9， 0）  1
（-8， 0）  3
（-7， 0）  3
（-6， 0）  3
（-5， 0）  3
（-4， 0）  3
（-3， 0）  3
（-2， 0）  3
（-1， 0）  3
（0， 0）  0
（1， 0）  1
（2， 0）  1
（3， 0）  1
（4， 0）  1
（5， 0）  1
（6， 0）  1
（7， 0）  1
（8， 0）  1
（9， 0）  3
（10， 0）  3
（11， 0）  3
（12， 0）  3
（13， 0）  3
（14， 0）  3
（15， 0）  3
（16， 0）  0

（-16， -1）  4
（-15， -1）  8
（-14， -1）  1
（-13， -1）  8
（-12， -1）  1
（-11， -1）  5
（-10， -1）  1
（-9， -1）  5
（-8， -1）  2
（-7， -1）  6
（-6， -1）  3
（-5， -1）  6
（-4， -1）  3
（-3， -1）  7
（-2， -1）  3
（-1， -1）  7
（0， -1）  4
（1， -1）  8
（2， -1）  1
（3， -1）  8
（4， -1）  1
（5， -1）  5
（6， -1）  1
（7， -1）  5
（8， -1）  2
（9， -1）  6
（10， -1）  3
（11， -1）  6
（12， -1）  3
（13， -1）  7
（14， -1）  3
（15， -1）  7
（16， -1）  4

（-16， -2）  4
（-15， -2）  4
（-14， -2）  8
（-13， -2）  1
（-12， -2）  1
（-11， -2）  1
（-10， -2）  5
（-9， -2）  2
（-8， -2）  2
（-7， -2）  2
（-6， -2）  6
（-5， -2）  3
（-4， -2）  3
（-3， -2）  3
（-2， -2）  7
（-1， -2）  4
（0， -2）  4
（1， -2）  4
（2， -2）  8
（3， -2）  1
（4， -2）  1
（5， -2）  1
（6， -2）  5
（7， -2）  2
（8， -2）  2
（9， -2）  2
（10， -2）  6
（11， -2）  3
（12， -2）  3
（13， -2）  3
（14， -2）  7
（15， -2）  4
（16， -2）  4

（-16， -3）  4
（-15， -3）  8
（-14， -3）  4
（-13， -3）  8
（-12， -3）  1
（-11， -3）  5
（-10， -3）  2
（-9， -3）  5
（-8， -3）  2
（-7， -3）  6
（-6， -3）  2
（-5， -3）  6
（-4， -3）  3
（-3， -3）  7
（-2， -3）  4
（-1， -3）  7
（0， -3）  4
（1， -3）  8
（2， -3）  4
（3， -3）  8
（4， -3）  1
（5， -3）  5
（6， -3）  2
（7， -3）  5
（8， -3）  2
（9， -3）  6
（10， -3）  2
（11， -3）  6
（12， -3）  3
（13， -3）  7
（14， -3）  4
（15， -3）  7
（16， -3）  4

（-16， -4）  4
（-15， -4）  4
（-14， -4）  4
（-13， -4）  4
（-12， -4）  6
（-11， -4）  2
（-10， -4）  2
（-9， -4）  2
（-8， -4）  2
（-7， -4）  2
（-6， -4）  2
（-5， -4）  2
（-4， -4）  7
（-3， -4）  4
（-2， -4）  4
（-1， -4）  4
（0， -4）  4
（1， -4）  4
（2， -4）  4
（3， -4）  4
（4， -4）  8
（5， -4）  2
（6， -4）  2
（7， -4）  2
（8， -4）  2
（9， -4）  2
（10， -4）  2
（11， -4）  2
（12， -4）  5
（13， -4）  4
（14， -4）  4
（15， -4）  4
（16， -4）  4

（-16， -5）  4
（-15， -5）  7
（-14， -5）  4
（-13， -5）  7
（-12， -5）  3
（-11， -5）  6
（-10， -5）  2
（-9， -5）  6
（-8， -5）  2
（-7， -5）  5
（-6， -5）  2
（-5， -5）  5
（-4， -5）  1
（-3， -5）  8
（-2， -5）  4
（-1， -5）  8
（0， -5）  4
（1， -5）  7
（2， -5）  4
（3， -5）  7
（4， -5）  3
（5， -5）  6
（6， -5）  2
（7， -5）  6
（8， -5）  2
（9， -5）  5
（10， -5）  2
（11， -5）  5
（12， -5）  1
（13， -5）  8
（14， -5）  4
（15， -5）  8
（16， -5）  4

（-16， -6）  4
（-15， -6）  4
（-14， -6）  7
（-13， -6）  3
（-12， -6）  3
（-11， -6）  3
（-10， -6）  6
（-9， -6）  2
（-8， -6）  2
（-7， -6）  2
（-6， -6）  5
（-5， -6）  1
（-4， -6）  1
（-3， -6）  1
（-2， -6）  8
（-1， -6）  4
（0， -6）  4
（1， -6）  4
（2， -6）  7
（3， -6）  3
（4， -6）  3
（5， -6）  3
（6， -6）  6
（7， -6）  2
（8， -6）  2
（9， -6）  2
（10， -6）  5
（11， -6）  1
（12， -6）  1
（13， -6）  1
（14， -6）  8
（15， -6）  4
（16， -6）  4

（-16， -7）  4
（-15， -7）  7
（-14， -7）  3
（-13， -7）  7
（-12， -7）  3
（-11， -7）  6
（-10， -7）  3
（-9， -7）  6
（-8， -7）  2
（-7， -7）  5
（-6， -7）  1
（-5， -7）  5
（-4， -7）  1
（-3， -7）  8
（-2， -7）  1
（-1， -7）  8
（0， -7）  4
（1， -7）  7
（2， -7）  3
（3， -7）  7
（4， -7）  3
（5， -7）  6
（6， -7）  3
（7， -7）  6
（8， -7）  2
（9， -7）  5
（10， -7）  1
（11， -7）  5
（12， -7）  1
（13， -7）  8
（14， -7）  1
（15， -7）  8
（16， -7）  4

（-16， -8）  2
（-15， -8）  3
（-14， -8）  3
（-13， -8）  3
（-12， -8）  3
（-11， -8）  3
（-10， -8）  3
（-9， -8）  3
（-8， -8）  0
（-7， -8）  1
（-6， -8）  1
（-5， -8）  1
（-4， -8）  1
（-3， -8）  1
（-2， -8）  1
（-1， -8）  1
（0， -8）  4
（1， -8）  3
（2， -8）  3
（3， -8）  3
（4， -8）  3
（5， -8）  3
（6， -8）  3
（7， -8）  3
（8， -8）  0
（9， -8）  1
（10， -8）  1
（11， -8）  1
（12， -8）  1
（13， -8）  1
（14， -8）  1
（15， -8）  1
（16， -8）  2

（-16， -9）  2
（-15， -9）  6
（-14， -9）  3
（-13， -9）  6
（-12， -9）  3
（-11， -9）  7
（-10， -9）  3
（-9， -9）  7
（-8， -9）  4
（-7， -9）  8
（-6， -9）  1
（-5， -9）  8
（-4， -9）  1
（-3， -9）  5
（-2， -9）  1
（-1， -9）  5
（0， -9）  2
（1， -9）  6
（2， -9）  3
（3， -9）  6
（4， -9）  3
（5， -9）  7
（6， -9）  3
（7， -9）  7
（8， -9）  4
（9， -9）  8
（10， -9）  1
（11， -9）  8
（12， -9）  1
（13， -9）  5
（14， -9）  1
（15， -9）  5
（16， -9）  2

（-16， -10）  2
（-15， -10）  2
（-14， -10）  6
（-13， -10）  3
（-12， -10）  3
（-11， -10）  3
（-10， -10）  7
（-9， -10）  4
（-8， -10）  4
（-7， -10）  4
（-6， -10）  8
（-5， -10）  1
（-4， -10）  1
（-3， -10）  1
（-2， -10）  5
（-1， -10）  2
（0， -10）  2
（1， -10）  2
（2， -10）  6
（3， -10）  3
（4， -10）  3
（5， -10）  3
（6， -10）  7
（7， -10）  4
（8， -10）  4
（9， -10）  4
（10， -10）  8
（11， -10）  1
（12， -10）  1
（13， -10）  1
（14， -10）  5
（15， -10）  2
（16， -10）  2

（-16， -11）  2
（-15， -11）  6
（-14， -11）  2
（-13， -11）  6
（-12， -11）  3
（-11， -11）  7
（-10， -11）  4
（-9， -11）  7
（-8， -11）  4
（-7， -11）  8
（-6， -11）  4
（-5， -11）  8
（-4， -11）  1
（-3， -11）  5
（-2， -11）  2
（-1， -11）  5
（0， -11）  2
（1， -11）  6
（2， -11）  2
（3， -11）  6
（4， -11）  3
（5， -11）  7
（6， -11）  4
（7， -11）  7
（8， -11）  4
（9， -11）  8
（10， -11）  4
（11， -11）  8
（12， -11）  1
（13， -11）  5
（14， -11）  2
（15， -11）  5
（16， -11）  2

（-16， -12）  2
（-15， -12）  2
（-14， -12）  2
（-15， -12）  2
（-12， -12）  5
（-11， -12）  4
（-10， -12）  4
（-9， -12）  4
（-8， -12）  4
（-7， -12）  4
（-6， -12）  4
（-5， -12）  4
（-4， -12）  8
（-3， -12）  2
（-2， -12）  2
（-1， -12）  2
（0， -12）  2
（1， -12）  2
（2， -12）  2
（3， -12）  2
（4， -12）  7
（5， -12）  4
（6， -12）  4
（7， -12）  4
（8， -12）  4
（9， -12）  4
（10， -12）  4
（11， -12）  4
（12， -12）  6
（13， -12）  2
（14， -12）  2
（15， -12）  2
（16， -12）  2

（-16， -13）  2
（-15， -13）  5
（-14， -13）  2
（-13， -13）  5
（-12， -13）  1
（-11， -13）  8
（-10， -13）  4
（-9， -13）  8
（-8， -13）  4
（-7， -13）  7
（-6， -13）  4
（-5， -13）  7
（-4， -13）  3
（-3， -13）  6
（-2， -13）  2
（-1， -13）  6
（0， -13）  2
（1， -13）  5
（2， -13）  2
（3， -13）  5
（4， -13）  1
（5， -13）  8
（6， -13）  4
（7， -13）  8
（8， -13）  4
（9， -13）  7
（10， -13）  4
（11， -13）  7
（12， -13）  3
（13， -13）  6
（14， -13）  2
（15， -13）  6
（16， -13）  2

（-16， -14）  2
（-15， -14）  2
（-14， -14）  5
（-13， -14）  1
（-12， -14）  1
（-11， -14）  1
（-10， -14）  8
（-9， -14）  4
（-8， -14）  4
（-7， -14）  4
（-6， -14）  7
（-5， -14）  3
（-4， -14）  3
（-3， -14）  3
（-2， -14）  6
（-1， -14）  2
（0， -14）  2
（1， -14）  2
（2， -14）  5
（3， -14）  1
（4， -14）  1
（5， -14）  1
（6， -14）  8
（7， -14）  4
（8， -14）  4
（9， -14）  4
（10， -14）  7
（11， -14）  3
（12， -14）  3
（13， -14）  3
（14， -14）  6
（15， -14）  2
（16， -14）  2

（-16， -15）  2
（-15， -15）  5
（-14， -15）  1
（-13， -15）  5
（-12， -15）  1
（-11， -15）  8
（-10， -15）  1
（-9， -15）  8
（-8， -15）  4
（-7， -15）  7
（-6， -15）  3
（-5， -15）  7
（-4， -15）  3
（-3， -15）  6
（-2， -15）  3
（-1， -15）  6
（0， -15）  2
（1， -15）  5
（2， -15）  1
（3， -15）  5
（4， -15）  1
（5， -15）  8
（6， -15）  1
（7， -15）  8
（8， -15）  4
（9， -15）  7
（10， -15）  3
（11， -15）  7
（12， -15）  3
（13， -15）  6
（14， -15）  3
（15， -15）  6
（16， -15）  2

（-16， -16）  0
（-15， -16）  1
（-14， -16）  1
（-13， -16）  1
（-12， -16）  1
（-11， -16）  1
（-10， -16）  1
（-9， -16）  1
（-8， -16）  1
（-7， -16）  3
（-6， -16）  3
（-5， -16）  3
（-4， -16）  3
（-3， -16）  3
（-2， -16）  3
（-1， -16）  3
（0， -16）  0
（1， -16）  1
（2， -16）  1
（3， -16）  1
（4， -16）  1
（5， -16）  1
（6， -16）  1
（7， -16）  1
（8， -16）  3
（9， -16）  3
（10， -16）  3
（11， -16）  3
（12， -16）  3
（13， -16）  3
（14， -16）  3
（15， -16）  3
（16， -16）  0