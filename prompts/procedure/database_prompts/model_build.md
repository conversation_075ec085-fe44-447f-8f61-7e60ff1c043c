请为我执行以下模型层和配置类构建操作：

- 创建app/models/__init__.py模型包初始化文件；
- 创建app/models/table.py表格模型文件；
- 创建app/models/text_info.py文本信息模型文件；
- 创建app/models/phrase.py词汇模型文件；
- 创建app/models/coordinate.py坐标模型文件；
- 确保app/config/database.py数据库配置正确；
- 确保app/config/settings.py应用设置正确；

模型定义要求：

Table模型：
- 表名：table_info
- id: BigInteger主键，索引
- name: String(255)非空
- create_time: DateTime默认当前时间，非空
- 关系：一对多关联Coordinate模型，级联删除

TextInfo模型：
- 表名：text_info
- id: BigInteger主键，索引
- color: Integer(0-8)非空，唯一约束
- text: String可空，默认空字符串
- 关系：一对多关联Phrase模型，级联删除

Phrase模型：
- 表名：phrase
- id: BigInteger主键，索引
- text_id: BigInteger外键关联TextInfo.id，非空，索引，字段名"textId"
- word: String(255)非空
- type: Integer非空，默认0
- 关系：多对一关联TextInfo模型

Coordinate模型：
- 表名：coordinate
- id: BigInteger主键，索引
- table_id: BigInteger外键关联Table.id，非空，索引，字段名"tableId"
- color: Integer非空
- position: String(255)非空
- voc: String(255)可空
- repeated: Integer非空，默认0
- 关系：多对一关联Table模型

技术要求：
- 使用SQLAlchemy ORM框架；
- 使用雪花算法生成ID；
- 提供完整的类型注解；
- 包含__repr__方法便于调试；
- 遵循PEP 8代码规范；
