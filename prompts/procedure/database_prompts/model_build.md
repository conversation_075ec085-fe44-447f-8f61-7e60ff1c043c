请为我构建SQLAlchemy模型层代码，要求如下：

- 创建app/models/目录下的所有模型文件和__init__.py；
- 确保app/config/目录下的数据库配置文件正确；

模型定义要求：

Table模型：
- 表名：table_info
- id: BigInteger主键，索引
- name: String(255)非空
- create_time: DateTime默认当前时间，非空
- 关系：一对多关联Coordinate模型，级联删除

TextInfo模型：
- 表名：text_info
- id: BigInteger主键，索引
- color: Integer(0-8)非空，唯一约束
- text: String可空，默认空字符串
- 关系：一对多关联Phrase模型，级联删除

Phrase模型：
- 表名：phrase
- id: BigInteger主键，索引
- text_id: BigInteger外键关联TextInfo.id，非空，索引，字段名"textId"
- word: String(255)非空
- type: Integer非空，默认0
- 关系：多对一关联TextInfo模型

Coordinate模型：
- 表名：coordinate
- id: BigInteger主键，索引
- table_id: BigInteger外键关联Table.id，非空，索引，字段名"tableId"
- color: Integer非空
- position: String(255)非空
- voc: String(255)可空
- repeated: Integer非空，默认0
- 关系：多对一关联Table模型

代码格式和风格要求：
- 使用SQLAlchemy ORM框架；
- 使用雪花算法生成ID；
- 提供完整的类型注解；
- 包含__repr__方法便于调试；
- 遵循PEP 8代码规范；
- 每个模型文件包含详细的文档字符串；
- 字段定义使用Column明确指定类型和约束；
- 关系定义使用relationship并指定back_populates；
