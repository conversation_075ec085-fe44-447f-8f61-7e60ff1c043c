请按照以下代码结构编写数据库相关代码：

- 先编写配置层：app/config/database.py数据库连接配置；
- 再编写模型层：app/models/各个实体模型文件；
- 最后编写模型包：app/models/__init__.py统一导入；

模型定义要求：

Table模型：
- 表名：table_info
- id: BigInteger主键，索引
- name: String(255)非空
- create_time: DateTime默认当前时间，非空
- 关系：一对多关联Coordinate模型，级联删除

TextInfo模型：
- 表名：text_info
- id: BigInteger主键，索引
- color: Integer(0-8)非空，唯一约束
- text: String可空，默认空字符串
- 关系：一对多关联Phrase模型，级联删除

Phrase模型：
- 表名：phrase
- id: BigInteger主键，索引
- text_id: BigInteger外键关联TextInfo.id，非空，索引，字段名"textId"
- word: String(255)非空
- type: Integer非空，默认0
- 关系：多对一关联TextInfo模型

Coordinate模型：
- 表名：coordinate
- id: BigInteger主键，索引
- table_id: BigInteger外键关联Table.id，非空，索引，字段名"tableId"
- color: Integer非空
- position: String(255)非空
- voc: String(255)可空
- repeated: Integer非空，默认0
- 关系：多对一关联Table模型

代码结构要求：

配置层结构（app/config/database.py）：
- 导入：sqlalchemy, declarative_base, sessionmaker；
- 定义DATABASE_URL = "sqlite:///./cube.db"；
- 创建engine with check_same_thread=False；
- 创建SessionLocal with autocommit=False, autoflush=False；
- 创建Base = declarative_base()；
- 定义get_db()生成器函数；
- 定义create_tables()函数；

模型层结构（每个模型文件）：
- 导入：Column, BigInteger, String等SQLAlchemy类型；
- 导入：relationship from sqlalchemy.orm；
- 导入：Base from app.config.database；
- 定义模型类继承Base；
- 设置__tablename__；
- 定义字段使用Column()；
- 定义关系使用relationship()和back_populates；
- 提供__repr__方法返回f字符串；

模型包结构（app/models/__init__.py）：
- 使用相对导入from .模块名 import 类名；
- 定义__all__列表包含所有模型类；
