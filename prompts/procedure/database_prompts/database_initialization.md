请为我创建数据库初始化系统，要求如下：

## 初始化脚本要求

### 主初始化脚本

文件位置：`data/init_database.py`

功能要求：
- 创建所有数据表结构
- 插入基础配置数据
- 提供数据库重置功能
- 支持开发和生产环境配置
- 包含完整的错误处理和日志记录

脚本结构：
```python
# 导入必要的模块
# 数据库连接和配置
# 表结构创建函数
# 基础数据插入函数
# 测试数据插入函数（可选）
# 数据库重置函数
# 主执行函数
# 命令行接口
```

### 基础数据初始化

**TextInfo 基础数据**：
- 创建 9 个 TextInfo 记录（color: 0-8）
- 每个记录的 text 字段初始为空字符串
- 使用雪花算法生成ID

**初始化数据示例**：
```python
base_text_infos = [
    {"color": 0, "text": ""},
    {"color": 1, "text": ""},
    {"color": 2, "text": ""},
    {"color": 3, "text": ""},
    {"color": 4, "text": ""},
    {"color": 5, "text": ""},
    {"color": 6, "text": ""},
    {"color": 7, "text": ""},
    {"color": 8, "text": ""}
]
```

### 测试数据（可选）

**测试表格数据**：
- 创建 2-3 个测试表格
- 表格名称：如 "测试表格1", "示例表格", "开发测试表"

**测试词汇数据**：
- 为部分 TextInfo 添加示例词汇
- 测试重复词汇的编号功能
- 验证文本处理逻辑

## 配置管理要求

### 环境配置

开发环境：
- 包含完整的测试数据
- 启用详细日志输出
- 支持数据库重置

生产环境：
- 只包含基础配置数据
- 简化日志输出
- 数据安全保护

### 配置文件更新

确保以下配置正确：
- `app/config/database.py` - 数据库连接配置
- `app/config/settings.py` - 应用设置
- `app/main.py` - 应用启动时的数据库初始化

## 脚本功能要求

### 命令行接口

支持以下命令：
```bash
# 初始化数据库（仅基础数据）
python data/init_database.py --init

# 重置数据库（删除所有数据重新初始化）
python data/init_database.py --reset

# 初始化包含测试数据
python data/init_database.py --init --with-test-data

# 检查数据库状态
python data/init_database.py --check
```

### 日志和错误处理

日志要求：
- 使用 Python logging 模块
- 记录初始化过程的关键步骤
- 错误时提供详细的错误信息
- 成功时显示统计信息

错误处理：
- 数据库连接失败处理
- 表创建失败回滚
- 数据插入失败处理
- 提供友好的错误提示

### 验证功能

数据完整性验证：
- 检查所有表是否创建成功
- 验证基础数据是否插入正确
- 检查外键关系是否正常
- 统计各表的记录数量

## 集成要求

### 应用启动集成

在 `app/main.py` 中集成：
- 应用启动时检查数据库状态
- 如果数据库不存在，自动初始化
- 如果表结构不完整，自动补全
- 提供启动日志信息

### 开发工具集成

提供便捷的开发工具：
- 数据库状态检查脚本
- 快速重置开发数据库
- 数据库备份和恢复工具
- 数据导入导出功能

## 输出文件清单

必需文件：
- `data/init_database.py` - 主初始化脚本
- `data/__init__.py` - Python包初始化
- `data/README.md` - 使用说明文档

可选文件：
- `data/test_data.sql` - SQL格式的测试数据
- `data/backup_restore.py` - 备份恢复工具
- `data/migration_tools.py` - 数据迁移工具

## 质量要求

代码质量：
- 完整的类型注解
- 详细的文档字符串
- 遵循 PEP 8 规范
- 包含单元测试

安全要求：
- 防止SQL注入
- 数据库连接安全
- 敏感信息保护
- 操作权限控制

性能要求：
- 批量数据插入优化
- 事务管理优化
- 内存使用控制
- 初始化速度优化
