请为我创建数据库初始化系统，要求如下：

## 数据库表结构设计

根据以下数据关系生成SQLite数据库表格代码：

**Table 表（表格信息）**：
- id: BigInteger, 主键
- name: String(255), 表格名称, 非空
- create_time: DateTime, 创建时间, 默认当前时间

**TextInfo 表（文本信息）**：
- id: BigInteger, 主键
- color: Integer, 颜色标识(0-8), 非空, 唯一
- text: String, 文本内容, 可空, 默认空字符串

**Phrase 表（词汇）**：
- id: BigInteger, 主键
- text_id: BigInteger, 外键关联TextInfo.id, 非空
- word: String(255), 词汇内容, 非空
- type: Integer, 词汇类型, 非空, 默认0

**Coordinate 表（坐标）**：
- id: BigInteger, 主键
- table_id: BigInteger, 外键关联Table.id, 非空, 字段名"tableId"
- color: Integer, 颜色标识(0-8), 非空
- position: String(255), 坐标位置, 非空
- voc: String(255), 词汇内容, 可空
- repeated: Integer, 重复次数, 非空, 默认0

## 数据关系

关系定义：
- TextInfo ↔ Phrase: 一对多关系
- Table ↔ Coordinate: 一对多关系
- 删除时级联删除关联记录

## 技术要求

使用技术：
- SQLAlchemy ORM框架
- SQLite数据库
- 雪花算法生成ID
- 数据库文件：./cube.db

## 输出文件

需要创建的文件：
- app/models/table.py
- app/models/text_info.py
- app/models/phrase.py
- app/models/coordinate.py
- app/models/__init__.py
- 确保app/config/database.py配置正确

## 代码规范

编码要求：
- 使用完整的类型注解
- 提供文档字符串
- 包含__repr__方法
- 遵循PEP 8规范
