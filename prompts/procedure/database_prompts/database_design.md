请为我设计和创建完整的数据库架构，要求如下：

## 技术要求

数据库框架：
- 使用 SQLAlchemy 2.0+ ORM框架
- 基于 SQLite 数据库
- 支持异步操作和连接池管理
- 使用雪花算法生成分布式唯一ID

## 数据模型设计

### 核心实体模型

**Table 模型（表格管理）**：
- id: BigInteger, 主键, 雪花算法ID
- name: String(255), 表格名称, 非空
- create_time: DateTime, 创建时间, 默认当前时间
- 关系: 一对多关联 Coordinate 模型

**TextInfo 模型（文本信息）**：
- id: BigInteger, 主键, 雪花算法ID  
- color: Integer, 颜色标识(0-8), 非空, 唯一约束
- text: String, 文本内容, 可空, 默认空字符串
- 关系: 一对多关联 Phrase 模型

**Phrase 模型（词汇管理）**：
- id: BigInteger, 主键, 雪花算法ID
- text_id: BigInteger, 外键关联 TextInfo.id, 非空, 索引
- word: String(255), 词汇内容, 非空
- type: Integer, 词汇类型/重复次数标识, 非空, 默认0
- 关系: 多对一关联 TextInfo 模型

**Coordinate 模型（坐标数据）**：
- id: BigInteger, 主键, 雪花算法ID
- table_id: BigInteger, 外键关联 Table.id, 非空, 索引, 字段名"tableId"
- color: Integer, 颜色标识(0-8), 非空
- position: String(255), 坐标位置格式"（x， y）", 非空
- voc: String(255), 词汇内容, 可空
- repeated: Integer, 重复次数, 非空, 默认0
- 关系: 多对一关联 Table 模型

## 关系设计要求

级联规则：
- Table 删除时，级联删除所有关联的 Coordinate 记录
- TextInfo 删除时，级联删除所有关联的 Phrase 记录
- 使用 "all, delete-orphan" 级联策略

索引策略：
- 所有主键自动创建索引
- 外键字段创建索引提高查询性能
- color 字段考虑创建索引（频繁查询）

## 数据库配置要求

连接配置：
- 数据库文件: ./cube.db
- 连接参数: check_same_thread=False (SQLite多线程支持)
- 会话管理: autocommit=False, autoflush=False

初始化要求：
- 自动创建所有表结构
- 支持应用启动时自动建表
- 提供数据库重置功能
- 包含完整的错误处理

## 输出文件要求

模型文件：
- app/models/table.py - Table模型定义
- app/models/text_info.py - TextInfo模型定义  
- app/models/phrase.py - Phrase模型定义
- app/models/coordinate.py - Coordinate模型定义
- app/models/__init__.py - 模型导入配置

配置文件：
- 确保 app/config/database.py 配置正确
- 确保 app/config/settings.py 数据库设置正确

代码规范：
- 使用完整的类型注解
- 提供详细的文档字符串
- 遵循 PEP 8 代码规范
- 包含 __repr__ 方法便于调试

## 特殊要求

字段命名：
- 保持与现有Java版本的兼容性
- 外键字段使用驼峰命名（如 tableId, textId）
- Python属性使用下划线命名（如 table_id, text_id）

数据完整性：
- 确保颜色值范围在0-8之间
- 确保字符串长度限制
- 提供适当的默认值
- 添加必要的约束检查
