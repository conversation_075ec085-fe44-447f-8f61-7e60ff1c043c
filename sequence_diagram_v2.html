<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词汇管理系统时序图 v2.0 - 技术实现版</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
            letter-spacing: 2px;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .version-info {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            font-size: 1.1em;
            border-left: 5px solid #2980b9;
        }

        .sequence-section {
            margin: 40px;
            border: 2px solid #e8f4f8;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .sequence-section h3 {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            margin: 0;
            padding: 20px 30px;
            font-size: 1.4em;
            font-weight: 500;
        }

        .mermaid {
            padding: 30px;
            background: #fafbfc;
            min-height: 1000px;
            overflow: visible;
        }

        .tech-notes {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .tech-notes h4 {
            color: #155724;
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }

        .tech-notes ul {
            margin: 0;
            padding-left: 20px;
        }

        .tech-notes li {
            margin: 5px 0;
            color: #155724;
        }

        .step-indicator {
            background: #e9ecef;
            border-left: 4px solid #6c757d;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }

        .step-indicator h5 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 1.1em;
        }

        .step-indicator p {
            margin: 0;
            color: #6c757d;
            font-size: 0.95em;
        }

        .architecture-overview {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .architecture-overview h4 {
            color: #856404;
            margin: 0 0 15px 0;
        }

        .layer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .layer-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
        }

        .layer-item h6 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 0.9em;
        }

        .layer-item p {
            margin: 0;
            font-size: 0.8em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>词汇管理系统时序图 v2.0</h1>
            <p>技术实现详细版 - 基于FastAPI + SQLAlchemy架构</p>
        </div>

        <div class="version-info">
            <strong>版本特性：</strong>
            ✨ 技术化参与者命名 | 🔧 具体方法调用展示 | 📊 详细算法实现 | 🗄️ 精确SQL语句 | 🎯 分组步骤编号 | 💡 激活框优化
        </div>

        <div class="architecture-overview">
            <h4>🏗️ 系统架构概览</h4>
            <div class="layer-grid">
                <div class="layer-item">
                    <h6>🌐 HTTP接口层</h6>
                    <p>PhraseController<br/>CoordinateController</p>
                </div>
                <div class="layer-item">
                    <h6>🔧 配置管理层</h6>
                    <p>DatabaseConfig<br/>SessionManager</p>
                </div>
                <div class="layer-item">
                    <h6>🔍 数据验证层</h6>
                    <p>PydanticValidator<br/>SchemaProcessor</p>
                </div>
                <div class="layer-item">
                    <h6>💼 业务逻辑层</h6>
                    <p>PhraseService<br/>CoordinateService</p>
                </div>
                <div class="layer-item">
                    <h6>🛠️ 工具算法层</h6>
                    <p>TextProcessor<br/>AlgorithmEngine</p>
                </div>
                <div class="layer-item">
                    <h6>📊 数据模型层</h6>
                    <p>SQLAlchemyORM<br/>PydanticSchema</p>
                </div>
                <div class="layer-item">
                    <h6>🗄️ 数据持久层</h6>
                    <p>DatabaseEngine<br/>TransactionManager</p>
                </div>
                <div class="layer-item">
                    <h6>📤 响应构建层</h6>
                    <p>ResponseBuilder<br/>JSONSerializer</p>
                </div>
            </div>
        </div>

        <!-- 词汇添加操作时序图 -->
        <div class="sequence-section">
            <h3>📝 词汇添加操作时序图 (add_phrase) - 技术实现版</h3>
            
            <div class="tech-notes">
                <h4>🔧 核心技术栈</h4>
                <ul>
                    <li><strong>Web框架：</strong>FastAPI 0.104+ (异步路由、依赖注入、自动文档)</li>
                    <li><strong>ORM框架：</strong>SQLAlchemy 2.0+ (声明式映射、会话管理、事务控制)</li>
                    <li><strong>数据验证：</strong>Pydantic v2 (类型安全、字段验证、序列化)</li>
                    <li><strong>算法实现：</strong>Python Collections.Counter (差异检测、文本分析)</li>
                </ul>
            </div>

            <div class="step-indicator">
                <h5>📋 操作流程概述</h5>
                <p>前端提交新文本 → 文本差异分析 → 智能编号处理 → 数据库事务提交 → 响应构建返回</p>
            </div>

            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant PhraseController as 词汇控制器
    participant DatabaseConfig as 数据库配置
    participant PydanticValidator as 数据验证器
    participant PhraseService as 词汇服务
    participant TextProcessor as 文本处理器
    participant SQLAlchemyORM as ORM映射器
    participant DatabaseEngine as 数据库引擎
    participant ResponseBuilder as 响应构建器

    %% HTTP请求阶段
    Note over Frontend, ResponseBuilder: 🚀 阶段1 HTTP请求处理与路由匹配
    Frontend->>+PhraseController: 1.1 调用 HTTP POST请求<br/>@router.post("/add")<br/>URL /phrase/add<br/>Headers Content-Type application/json<br/>Body {color 0, text "苹果、香蕉、橙子、"}

    %% FastAPI依赖注入系统
    PhraseController->>+DatabaseConfig: 1.2 调用 依赖注入获取会话<br/>async def add_phrase(db Session = Depends(get_db))
    DatabaseConfig->>DatabaseConfig: 1.3 执行 数据库引擎初始化<br/>engine = create_engine(DATABASE_URL)<br/>connect_args={"check_same_thread" False}
    DatabaseConfig->>DatabaseConfig: 1.4 执行 会话工厂创建<br/>SessionLocal = sessionmaker(<br/>  autocommit=False, autoflush=False, bind=engine)
    DatabaseConfig->>DatabaseConfig: 1.5 执行 生成器函数<br/>def get_db()<br/>  db = SessionLocal()<br/>  try yield db<br/>  finally db.close()
    DatabaseConfig-->>-PhraseController: 返回 SQLAlchemy Session对象

    %% Pydantic数据验证阶段
    Note over PhraseController, PydanticValidator: 🔍 阶段2 数据验证与类型转换
    PhraseController->>+PydanticValidator: 2.1 调用 请求体自动验证<br/>text_info TextInfoColorUpdate
    PydanticValidator->>PydanticValidator: 2.2 处理 字段定义验证<br/>class TextInfoColorUpdate(BaseModel)<br/>  color int = Field(ge=0, le=8)<br/>  text str = Field(max_length=1000)
    PydanticValidator->>PydanticValidator: 2.3 处理 JSON反序列化<br/>JSON → Python对象转换<br/>类型检查与约束验证
    PydanticValidator-->>-PhraseController: 返回 TextInfoColorUpdate(color=0, text="苹果、香蕉、橙子、")

    %% 业务逻辑处理阶段
    Note over PhraseController, TextProcessor: 💼 阶段3 业务逻辑与服务层处理
    PhraseController->>+PhraseService: 3.1 调用 词汇添加业务逻辑<br/>service = PhraseService(db)<br/>await service.add_phrase(text_info.color, text_info.text)

    %% 数据库查询获取旧文本
    PhraseService->>+SQLAlchemyORM: 3.2 调用 查询现有文本信息<br/>self._db.query(TextInfo).filter(TextInfo.color == color).first()
    SQLAlchemyORM->>+DatabaseEngine: 3.3 执行 SQL查询语句<br/>SELECT text_info.id, text_info.color, text_info.text<br/>FROM text_info WHERE text_info.color = ?<br/>LIMIT 1
    DatabaseEngine-->>-SQLAlchemyORM: 返回 数据库查询结果<br/>Row(id=123, color=0, text="苹果、香蕉、")
    SQLAlchemyORM-->>-PhraseService: 返回 TextInfo对象<br/>TextInfo(id=123, color=0, text="苹果、香蕉、")

    %% 文本分割算法实现
    PhraseService->>+TextProcessor: 3.4 调用 文本分割算法<br/>TextProcessor.split_text_by_comma(old_text)
    TextProcessor->>TextProcessor: 3.5 处理 顿号分割算法<br/>parts = text.split("、")<br/>for i, part in enumerate(parts)<br/>  if i == len(parts) - 1<br/>    if text.endswith("、") blocks.append(part)<br/>    else tail_text = part<br/>  else blocks.append(part)
    TextProcessor-->>-PhraseService: 返回 TextSplitResult<br/>blocks=["苹果", "香蕉"], tail_text=""

    %% 差异检测算法实现
    PhraseService->>+TextProcessor: 3.6 调用 差异检测算法<br/>TextProcessor.find_different_blocks(new_blocks, old_blocks)
    TextProcessor->>TextProcessor: 3.7 处理 Counter差异分析<br/>from collections import Counter<br/>source_count = Counter(new_blocks)<br/>target_count = Counter(old_blocks)<br/>for block, source_num in source_count.items()<br/>  diff = source_num - target_count.get(block, 0)<br/>  differences.extend([block] * diff)
    TextProcessor-->>-PhraseService: 返回 差异词汇列表<br/>["橙子"]

    %% 智能编号处理算法
    alt diff_phrase_list is not empty
        Note over PhraseService, DatabaseEngine: 🎯 阶段4 智能编号算法与数据库操作

        %% 获取索引映射算法
        PhraseService->>+TextProcessor: 3.8 调用 获取块索引映射<br/>TextProcessor.get_block_index_map(text_blocks, diff_blocks)
        TextProcessor->>TextProcessor: 3.9 处理 索引映射算法<br/>index_map = {}<br/>for diff_block in diff_blocks<br/>  matching_indices = [i for i, block in enumerate(text_blocks)<br/>                     if block == diff_block]<br/>  selected_index = matching_indices[-(used_times + 1)]<br/>  index_map[f"{diff_block}_{used_times}"] = selected_index
        TextProcessor-->>-PhraseService: 返回 索引映射字典<br/>{"橙子_0" 2}

        %% 智能编号算法实现
        loop for each diff phrase
            PhraseService->>+SQLAlchemyORM: 3.10 调用 查询现有同名词汇<br/>self._db.query(Phrase).filter(Phrase.word == word).all()
            SQLAlchemyORM->>+DatabaseEngine: 3.11 执行 SQL查询语句<br/>SELECT phrase.id, phrase.text_id, phrase.word, phrase.type<br/>FROM phrase WHERE phrase.word = ?<br/>参数 ["橙子"]
            DatabaseEngine-->>-SQLAlchemyORM: 返回 查询结果集<br/>[] (新词汇无现有记录)
            SQLAlchemyORM-->>-PhraseService: 返回 Phrase对象列表<br/>all_phrases = []

            PhraseService->>PhraseService: 3.12 处理 智能编号计算<br/>word = self._extract_word_from_block(key)<br/>count = sum(1 for phrase in all_phrases if phrase.word == word)<br/>type_value = count  # 新词汇type=0

            %% 创建新Phrase对象
            PhraseService->>+SQLAlchemyORM: 3.13 调用 创建新词汇实例<br/>phrase = Phrase(<br/>  id=generate_id(),<br/>  text_id=text_info_id,<br/>  word="橙子",<br/>  type=0<br/>)
            SQLAlchemyORM->>SQLAlchemyORM: 3.14 处理 SQLAlchemy ORM实例化<br/>class Phrase(Base)<br/>  __tablename__ = "phrase"<br/>  id = Column(BigInteger, primary_key=True)<br/>  text_id = Column(BigInteger, ForeignKey("text_info.id"))<br/>  word = Column(String(255), nullable=False)<br/>  type = Column(Integer, nullable=False)
            SQLAlchemyORM-->>-PhraseService: 返回 新Phrase对象实例

            %% 数据库插入操作
            PhraseService->>+DatabaseEngine: 3.15 执行 SQL插入语句<br/>INSERT INTO phrase (id, textId, word, type)<br/>VALUES (?, ?, ?, ?)<br/>参数 [456, 123, "橙子", 0]
            DatabaseEngine-->>-PhraseService: 返回 插入操作成功 ✅
        end

        %% 更新TextInfo文本内容
        PhraseService->>+SQLAlchemyORM: 3.16 调用 更新文本信息<br/>old_text_info.text = final_text<br/>final_text = "、".join(modified_blocks) + "、" + tail_text
        SQLAlchemyORM->>+DatabaseEngine: 3.17 执行 SQL更新语句<br/>UPDATE text_info SET text = ?<br/>WHERE text_info.id = ?<br/>参数 ["苹果、香蕉、橙子、", 123]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 更新操作成功 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 更新后的TextInfo对象

        %% 事务提交确保数据一致性
        PhraseService->>+DatabaseEngine: 3.18 调用 事务提交<br/>self._db.commit()
        DatabaseEngine->>DatabaseEngine: 3.19 执行 数据库事务提交<br/>BEGIN TRANSACTION<br/>  INSERT INTO phrase...<br/>  UPDATE text_info...<br/>COMMIT TRANSACTION
        DatabaseEngine-->>-PhraseService: 返回 事务提交成功 ✅

        PhraseService-->>PhraseController: 返回 业务逻辑处理结果<br/>{"message" "添加成功",<br/> "text_info" {<br/>   "id" 123, "color" 0,<br/>   "text" "苹果、香蕉、橙子、"<br/> }}
    else no new phrases
        PhraseService-->>PhraseController: 返回 无变化业务结果<br/>{"message" "没有新增词汇",<br/> "text_info" current_text_info}
    end

    %% 响应构建与序列化阶段
    Note over PhraseController, ResponseBuilder: 📤 阶段5 HTTP响应构建与JSON序列化
    PhraseController->>+ResponseBuilder: 4.1 调用 FastAPI自动响应构建<br/>return result  # FastAPI自动处理JSON序列化
    ResponseBuilder->>ResponseBuilder: 4.2 处理 Pydantic响应模型<br/>class TextInfoResponse(BaseModel)<br/>  id int<br/>  color int<br/>  text str<br/>  @field_serializer('id')<br/>  def serialize_id(value) -> str return str(value)
    ResponseBuilder->>ResponseBuilder: 4.3 处理 JSON序列化<br/>json.dumps(result, ensure_ascii=False)<br/>Content-Type application/json charset=utf-8<br/>Status 200 OK
    ResponseBuilder-->>-PhraseController: 返回 JSONResponse对象

    %% 资源清理与会话管理
    PhraseController->>+DatabaseConfig: 4.4 调用 依赖注入清理<br/>finally块自动执行 db.close()
    DatabaseConfig->>DatabaseConfig: 4.5 处理 SQLAlchemy会话清理<br/>session.close()<br/>连接池归还 engine.dispose()
    DatabaseConfig-->>-PhraseController: 返回 资源清理完成 ✅

    %% HTTP响应返回
    PhraseController-->>-Frontend: 4.6 返回 HTTP 200 OK响应<br/>Headers Content-Type application/json<br/>Body {<br/>  "message" "添加成功",<br/>  "text_info" {<br/>    "id" "123", "color" 0,<br/>    "text" "苹果、香蕉、橙子、"<br/>  }<br/>}
</div>
        </div>

        <!-- 词汇删除操作时序图 -->
        <div class="sequence-section">
            <h3>🗑️ 词汇删除操作时序图 (delete_phrase) - 技术实现版</h3>

            <div class="tech-notes">
                <h4>🔧 核心算法特性</h4>
                <ul>
                    <li><strong>差异检测：</strong>Collections.Counter统计算法 (O(n)时间复杂度)</li>
                    <li><strong>模式识别：</strong>正则表达式 r'^(.+?)(\d+)$' 解析词汇编号</li>
                    <li><strong>重编号策略：</strong>全局一致性维护，级联更新相关记录</li>
                    <li><strong>事务管理：</strong>ACID特性保证，支持自动回滚机制</li>
                </ul>
            </div>

            <div class="step-indicator">
                <h5>📋 删除策略分类</h5>
                <p><strong>简单删除：</strong>无编号后缀词汇 (如"香蕉") | <strong>复杂删除：</strong>带编号后缀词汇 (如"苹果2") + 全局重编号</p>
            </div>

            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant PhraseController as 词汇控制器
    participant DatabaseConfig as 数据库配置
    participant PydanticValidator as 数据验证器
    participant PhraseService as 词汇服务
    participant TextProcessor as 文本处理器
    participant SQLAlchemyORM as ORM映射器
    participant DatabaseEngine as 数据库引擎
    participant ResponseBuilder as 响应构建器

    %% HTTP请求阶段
    Note over Frontend, ResponseBuilder: 🚀 阶段1 HTTP请求处理
    Frontend->>+PhraseController: 1.1 调用 DELETE /phrase/delete<br/>请求体 {color 0, text "苹果、苹果3、香蕉、"}

    %% 依赖注入和验证
    PhraseController->>+DatabaseConfig: 1.2 调用 获取数据库会话<br/>依赖注入 get_db()
    DatabaseConfig->>DatabaseConfig: 1.3 执行 数据库引擎初始化<br/>engine = create_engine(DATABASE_URL)<br/>SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    DatabaseConfig->>DatabaseConfig: 1.4 执行 生成器函数<br/>def get_db()<br/>  db = SessionLocal()<br/>  try yield db<br/>  finally db.close()
    DatabaseConfig-->>-PhraseController: 返回 SQLAlchemy Session对象

    PhraseController->>+PydanticValidator: 1.5 调用 数据模型验证<br/>TextInfoColorUpdate.model_validate()
    PydanticValidator->>PydanticValidator: 1.6 处理 字段定义验证<br/>class TextInfoColorUpdate(BaseModel)<br/>  color int = Field(ge=0, le=8)<br/>  text str = Field(max_length=1000)
    PydanticValidator->>PydanticValidator: 1.7 处理 JSON反序列化<br/>JSON → Python对象转换<br/>类型检查与约束验证
    PydanticValidator-->>-PhraseController: 返回 TextInfoColorUpdate(color=0, text="苹果、苹果3、香蕉、")

    %% 业务逻辑处理阶段
    Note over PhraseController, TextProcessor: 💼 阶段2 业务逻辑与文本分析
    PhraseController->>+PhraseService: 2.1 调用 词汇删除服务<br/>service = PhraseService(db)<br/>await service.delete_phrase(text_info.color, text_info.text)

    %% 获取当前文本
    PhraseService->>+SQLAlchemyORM: 2.2 调用 查询当前文本信息<br/>self._db.query(TextInfo).filter(TextInfo.color == color).first()
    SQLAlchemyORM->>+DatabaseEngine: 2.3 执行 SQL查询<br/>SELECT text_info.id, text_info.color, text_info.text<br/>FROM text_info WHERE text_info.color = ?<br/>LIMIT 1
    DatabaseEngine-->>-SQLAlchemyORM: 返回 数据库查询结果<br/>Row(id=123, color=0, text="苹果、苹果2、苹果3、香蕉、")
    SQLAlchemyORM-->>-PhraseService: 返回 TextInfo对象<br/>TextInfo(id=123, color=0, text="苹果、苹果2、苹果3、香蕉、")

    %% 文本差异分析
    PhraseService->>+TextProcessor: 2.4 调用 文本分割算法<br/>TextProcessor.split_text_by_comma(old_text)
    TextProcessor->>TextProcessor: 2.5 处理 顿号分割算法<br/>parts = text.split("、")<br/>for i, part in enumerate(parts)<br/>  if i == len(parts) - 1<br/>    if text.endswith("、") blocks.append(part)<br/>    else tail_text = part<br/>  else blocks.append(part)
    TextProcessor-->>-PhraseService: 返回 TextSplitResult<br/>old_blocks=["苹果", "苹果2", "苹果3", "香蕉"], tail_text=""

    PhraseService->>+TextProcessor: 2.6 调用 文本分割算法<br/>TextProcessor.split_text_by_comma(new_text)
    TextProcessor-->>-PhraseService: 返回 TextSplitResult<br/>new_blocks=["苹果", "苹果3", "香蕉"], tail_text=""

    %% 差异检测
    PhraseService->>+TextProcessor: 2.7 调用 差异检测算法<br/>TextProcessor.find_different_blocks(old_blocks, new_blocks)
    TextProcessor->>TextProcessor: 2.8 处理 Counter差异分析<br/>from collections import Counter<br/>source_count = Counter(old_blocks)<br/>target_count = Counter(new_blocks)<br/>for block, source_num in source_count.items()<br/>  target_num = target_count.get(block, 0)<br/>  diff = source_num - target_num<br/>  differences.extend([block] * diff)
    TextProcessor-->>-PhraseService: 返回 删除词汇列表<br/>deleted_blocks = ["苹果2"]

    %% 删除策略判断
    Note over PhraseService, DatabaseEngine: 🎯 阶段3 删除策略与模式识别
    PhraseService->>PhraseService: 3.1 处理 删除策略选择<br/>deleted_block = deleted_blocks[0]<br/>has_digit_suffix = bool(re.search(r'\d$', deleted_block))
    PhraseService->>PhraseService: 3.2 处理 模式分析<br/>match = re.search(r'^(.+?)(\d+)$', deleted_block)<br/>word = match.group(1)  # "苹果"<br/>type_value = int(match.group(2))  # 2

    alt with digit suffix
        Note over PhraseService, DatabaseEngine: 🔄 复杂删除与全局重编号

        %% 删除目标记录
        PhraseService->>+SQLAlchemyORM: 3.3 调用 删除目标词汇<br/>self._delete_phrase_by_word_and_type(word, type_value, text_info_id)
        SQLAlchemyORM->>+DatabaseEngine: 3.4 执行 SQL删除<br/>DELETE FROM phrase<br/>WHERE word = ? AND type = ? AND text_id = ?<br/>参数 ["苹果", 2, 123]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 删除成功 ✅ (1行受影响)
        SQLAlchemyORM-->>-PhraseService: 返回 删除操作成功

        %% 更新当前TextInfo
        PhraseService->>+SQLAlchemyORM: 3.5 调用 更新当前文本信息<br/>self._db.query(TextInfo).filter(TextInfo.id == text_info_id).update({"text" new_text})
        SQLAlchemyORM->>+DatabaseEngine: 3.6 执行 SQL更新<br/>UPDATE text_info SET text = ?<br/>WHERE id = ?<br/>参数 ["苹果、苹果3、香蕉、", 123]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 更新成功 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 当前文本已更新

        %% 查询需要重编号的记录
        PhraseService->>+SQLAlchemyORM: 3.7 调用 查询重编号词汇<br/>self._db.query(Phrase).filter(<br/>  Phrase.word == word,<br/>  Phrase.type > type_value<br/>).order_by(Phrase.type.asc()).all()
        SQLAlchemyORM->>+DatabaseEngine: 3.8 执行 SQL查询<br/>SELECT id, word, type, text_id FROM phrase<br/>WHERE word = ? AND type > ?<br/>ORDER BY type ASC<br/>参数 ["苹果", 2]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 查询结果<br/>[Phrase(word="苹果", type=3, text_id=123)]
        SQLAlchemyORM-->>-PhraseService: 返回 待更新词汇列表<br/>phrase_list_to_update

        %% 全局重编号处理
        PhraseService->>+SQLAlchemyORM: 3.9 调用 查询所有相关TextInfo<br/>text_info_ids = [phrase.text_id for phrase in phrase_list_to_update]<br/>self._db.query(TextInfo).filter(TextInfo.id.in_(text_info_ids)).all()
        SQLAlchemyORM->>+DatabaseEngine: 3.10 执行 SQL查询<br/>SELECT id, text FROM text_info<br/>WHERE id IN (123)<br/>参数 [123]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 受影响的文本信息列表
        SQLAlchemyORM-->>-PhraseService: 返回 全局文本记录<br/>text_info_dict = {text_info.id text_info}

        loop for each phrase to update
            PhraseService->>PhraseService: 3.11 处理 重编号计算<br/>original_type = phrase.type  # 3<br/>new_type = original_type - 1  # 2<br/>old_word = phrase.word + str(original_type)  # "苹果3"<br/>new_word = phrase.word + (str(new_type) if new_type > 0 else "")  # "苹果2"

            %% 全局文本替换
            PhraseService->>PhraseService: 3.12 处理 字符串替换<br/>text_info = text_info_dict.get(phrase.text_id)<br/>current_text = text_info.text or ""<br/>new_word_text = current_text.replace(old_word, new_word)<br/>text_info.text = new_word_text

            %% 更新Phrase的type值
            PhraseService->>PhraseService: 3.13 处理 更新词汇类型<br/>phrase.type = new_type
        end

        %% 批量更新数据库
        PhraseService->>+SQLAlchemyORM: 3.14 调用 批量更新TextInfo<br/>for text_info in text_info_dict.values()<br/>  self._db.merge(text_info)
        SQLAlchemyORM->>+DatabaseEngine: 3.15 执行 批量SQL更新<br/>UPDATE text_info SET text = ? WHERE id = ?<br/>批量参数 [("苹果、苹果2、香蕉、", 123)]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 批量更新成功 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 全局文本同步完成

        PhraseService->>+SQLAlchemyORM: 3.16 调用 批量更新Phrase<br/>for phrase in phrase_list_to_update<br/>  self._db.merge(phrase)
        SQLAlchemyORM->>+DatabaseEngine: 3.17 执行 批量SQL更新<br/>UPDATE phrase SET type = ? WHERE id = ?<br/>批量参数 [(2, phrase_id)]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 批量更新成功 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 词汇重编号完成

        %% 事务提交
        PhraseService->>+DatabaseEngine: 3.18 调用 事务提交<br/>self._db.commit()
        DatabaseEngine->>DatabaseEngine: 3.19 执行 提交事务<br/>BEGIN TRANSACTION<br/>  DELETE FROM phrase...<br/>  UPDATE text_info...<br/>  UPDATE phrase...<br/>COMMIT TRANSACTION
        DatabaseEngine-->>-PhraseService: 返回 复杂删除完成 ✅

        PhraseService-->>PhraseController: 返回 复杂删除结果<br/>{"message" "删除成功", "updated_text_infos" updated_text_infos}

    else simple deletion
        Note over PhraseService, DatabaseEngine: ⚡ 简单删除流程

        PhraseService->>+SQLAlchemyORM: 调用 简单词汇删除<br/>self._handle_delete_without_digit(deleted_block, text_info_id, new_text)
        SQLAlchemyORM->>+DatabaseEngine: 执行 SQL删除<br/>DELETE FROM phrase<br/>WHERE word = ? AND type = 0 AND text_id = ?<br/>参数 [deleted_block, text_info_id]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 删除完成 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 简单删除成功

        PhraseService->>+SQLAlchemyORM: 调用 更新当前文本<br/>self._db.query(TextInfo).filter(TextInfo.id == text_info_id).update({"text" new_text})
        SQLAlchemyORM->>+DatabaseEngine: 执行 SQL更新<br/>UPDATE text_info SET text = ? WHERE id = ?<br/>参数 [new_text, text_info_id]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 更新成功 ✅
        SQLAlchemyORM-->>-PhraseService: 返回 文本更新完成

        PhraseService->>+DatabaseEngine: 调用 事务提交<br/>self._db.commit()
        DatabaseEngine-->>-PhraseService: 返回 简单删除事务完成 ✅

        PhraseService-->>PhraseController: 返回 简单删除结果<br/>{"message" "删除成功"}
    end

    %% 响应构建阶段
    Note over PhraseController, ResponseBuilder: 📤 阶段4 响应构建与清理
    PhraseController->>+ResponseBuilder: 4.1 调用 构建删除响应<br/>return result  # FastAPI自动处理JSON序列化
    ResponseBuilder->>ResponseBuilder: 4.2 处理 响应类型判断<br/>if "updated_text_infos" in result<br/>  复杂删除响应<br/>else<br/>  简单删除响应
    ResponseBuilder->>ResponseBuilder: 4.3 处理 JSON序列化<br/>json.dumps(result, ensure_ascii=False)<br/>Content-Type application/json charset=utf-8<br/>Status 200 OK
    ResponseBuilder-->>-PhraseController: 返回 格式化的JSONResponse

    %% 资源清理和响应
    PhraseController->>+DatabaseConfig: 4.4 调用 会话清理<br/>finally块自动执行 db.close()
    DatabaseConfig->>DatabaseConfig: 4.5 处理 SQLAlchemy会话清理<br/>session.close()<br/>连接池归还 engine.dispose()
    DatabaseConfig-->>-PhraseController: 返回 资源释放完成 ✅

    PhraseController-->>-Frontend: 4.6 返回 HTTP 200 OK响应<br/>Headers Content-Type application/json<br/>Body {<br/>  "message" "删除成功",<br/>  "updated_text_infos" [...] (复杂删除时)<br/>}
</div>
        </div>

        <!-- 坐标更新操作时序图 -->
        <div class="sequence-section">
            <h3>📍 坐标更新操作时序图 (coordinate_update) - 技术实现版</h3>

            <div class="tech-notes">
                <h4>🔧 坐标系统特性</h4>
                <ul>
                    <li><strong>坐标格式：</strong>中文格式 "（x， y）" 支持负数坐标</li>
                    <li><strong>重复值计算：</strong>同表同色同词汇的坐标数量统计</li>
                    <li><strong>文件解析：</strong>正则表达式解析cor.txt坐标文件</li>
                    <li><strong>批量处理：</strong>支持大量坐标数据的分批插入优化</li>
                </ul>
            </div>

            <div class="step-indicator">
                <h5>📋 更新触发条件</h5>
                <p><strong>voc变化：</strong>词汇内容更新触发重复值重新计算 | <strong>位置变化：</strong>坐标位置更新 | <strong>颜色变化：</strong>颜色标识更新</p>
            </div>

            <div class="mermaid">
sequenceDiagram
    participant Frontend as 前端客户端
    participant CoordinateController as 坐标控制器
    participant DatabaseConfig as 数据库配置
    participant PydanticValidator as 数据验证器
    participant CoordinateService as 坐标服务
    participant SQLAlchemyORM as ORM映射器
    participant DatabaseEngine as 数据库引擎
    participant ResponseBuilder as 响应构建器

    %% HTTP请求阶段
    Note over Frontend, ResponseBuilder: 🚀 阶段1 HTTP请求处理
    Frontend->>+CoordinateController: 1.1 调用 PUT /coordinate/update<br/>请求体 {id 456, table_id 1, color 0, position "（100， 200）", voc "香蕉"}

    %% 依赖注入和验证
    CoordinateController->>+DatabaseConfig: 1.2 调用 获取数据库会话<br/>依赖注入 get_db()
    DatabaseConfig->>DatabaseConfig: 1.3 执行 数据库引擎初始化<br/>engine = create_engine(DATABASE_URL)<br/>SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    DatabaseConfig->>DatabaseConfig: 1.4 执行 生成器函数<br/>def get_db()<br/>  db = SessionLocal()<br/>  try yield db<br/>  finally db.close()
    DatabaseConfig-->>-CoordinateController: 返回 SQLAlchemy Session对象

    CoordinateController->>+PydanticValidator: 1.5 调用 数据模型验证<br/>CoordinateUpdate.model_validate()
    PydanticValidator->>PydanticValidator: 1.6 处理 字段定义验证<br/>class CoordinateUpdate(BaseModel)<br/>  id int<br/>  table_id int<br/>  color int<br/>  position str<br/>  voc Optional[str] = ""<br/>  repeated int = 0
    PydanticValidator->>PydanticValidator: 1.7 处理 JSON反序列化<br/>JSON → Python对象转换<br/>类型检查与约束验证
    PydanticValidator-->>-CoordinateController: 返回 CoordinateUpdate对象

    %% 业务逻辑处理阶段
    Note over CoordinateController, SQLAlchemyORM: 💼 阶段2 业务逻辑与数据处理
    CoordinateController->>+CoordinateService: 2.1 调用 坐标更新服务<br/>service = CoordinateService(db)<br/>await service.update_coordinate(coordinate.id, coordinate.model_dump(exclude={"id"}))

    %% 查询当前坐标
    CoordinateService->>+SQLAlchemyORM: 2.2 调用 查询现有坐标<br/>self._db.query(Coordinate).filter(Coordinate.id == coordinate_id).first()
    SQLAlchemyORM->>+DatabaseEngine: 2.3 执行 SQL查询<br/>SELECT coordinate.id, coordinate.table_id, coordinate.color,<br/>       coordinate.position, coordinate.voc, coordinate.repeated<br/>FROM coordinate WHERE coordinate.id = ?<br/>参数 [456]
    DatabaseEngine-->>-SQLAlchemyORM: 返回 查询结果<br/>Row(id=456, table_id=1, color=0, position="（50， 100）", voc="苹果", repeated=1)
    SQLAlchemyORM-->>-CoordinateService: 返回 Coordinate对象<br/>coordinate = Coordinate(id=456, voc="苹果", color=0, repeated=1)

    %% 保存旧值用于变化检测
    CoordinateService->>CoordinateService: 2.4 处理 变化检测准备<br/>old_voc = coordinate.voc  # "苹果"<br/>new_voc = coordinate_data.get("voc", "")  # "香蕉"<br/>voc_changed = (old_voc != new_voc)

    %% 更新坐标基本信息
    CoordinateService->>CoordinateService: 2.5 处理 坐标字段更新<br/>coordinate.table_id = coordinate_data.get("table_id", coordinate.table_id)<br/>coordinate.color = coordinate_data.get("color", coordinate.color)<br/>coordinate.position = coordinate_data.get("position", coordinate.position)<br/>coordinate.voc = coordinate_data.get("voc", "")

    %% 重复值计算阶段
    Note over CoordinateService, DatabaseEngine: 🎯 阶段3 重复值计算算法
    alt voc is not empty
        CoordinateService->>+SQLAlchemyORM: 3.1 调用 统计相同词汇坐标<br/>self._db.query(Coordinate).filter(<br/>  and_(<br/>    Coordinate.table_id == coordinate.table_id,<br/>    Coordinate.voc == coordinate.voc,<br/>    Coordinate.color == coordinate.color<br/>  )<br/>).count()
        SQLAlchemyORM->>+DatabaseEngine: 3.2 执行 SQL统计查询<br/>SELECT COUNT(*) AS count_1<br/>FROM coordinate<br/>WHERE coordinate.table_id = ? AND coordinate.voc = ? AND coordinate.color = ?<br/>参数 [1, "香蕉", 0]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 统计结果<br/>count_result = 2
        SQLAlchemyORM-->>-CoordinateService: 返回 相同词汇数量<br/>same_voc_count = 2

        CoordinateService->>CoordinateService: 3.3 处理 设置重复值<br/>coordinate.repeated = same_voc_count  # 2
    else voc is empty
        CoordinateService->>CoordinateService: 3.4 处理 重置重复值<br/>coordinate.repeated = 0
    end

    %% 数据库持久化阶段
    Note over CoordinateService, DatabaseEngine: 🗄️ 阶段4 数据库持久化
    CoordinateService->>+SQLAlchemyORM: 4.1 调用 持久化坐标变更<br/>self._db.commit()
    SQLAlchemyORM->>+DatabaseEngine: 4.2 执行 SQL更新<br/>UPDATE coordinate SET<br/>  table_id = ?, color = ?, position = ?,<br/>  voc = ?, repeated = ?<br/>WHERE id = ?<br/>参数 [1, 0, "（100， 200）", "香蕉", 2, 456]
    DatabaseEngine-->>-SQLAlchemyORM: 返回 更新成功 ✅ (1行受影响)
    SQLAlchemyORM-->>-CoordinateService: 返回 更新操作成功

    %% 处理voc变化的级联更新
    alt voc_changed and old_voc is not empty
        Note over CoordinateService, DatabaseEngine: 🔄 旧词汇级联更新

        CoordinateService->>+SQLAlchemyORM: 4.3 调用 查询旧词汇坐标<br/>self._update_repeated_values_after_voc_change(table_id, old_voc, color)
        SQLAlchemyORM->>+DatabaseEngine: 4.4 执行 SQL查询<br/>SELECT id, table_id, color, position, voc, repeated<br/>FROM coordinate<br/>WHERE table_id = ? AND voc = ? AND color = ?<br/>ORDER BY repeated ASC<br/>参数 [1, "苹果", 0]
        DatabaseEngine-->>-SQLAlchemyORM: 返回 旧词汇坐标列表<br/>[Coordinate(id=123, repeated=0), Coordinate(id=124, repeated=1)]
        SQLAlchemyORM-->>-CoordinateService: 返回 需要重新编号的坐标

        %% 重新编号算法
        loop for i, coord in enumerate(coordinates)
            CoordinateService->>CoordinateService: 4.5 处理 重新编号算法<br/>for i, coord in enumerate(coordinates)<br/>  coord.repeated = i<br/>连续编号 0, 1, 2...

            CoordinateService->>+SQLAlchemyORM: 4.6 调用 更新重复值<br/>coord.repeated = new_index
            SQLAlchemyORM->>+DatabaseEngine: 4.7 执行 SQL更新<br/>UPDATE coordinate SET repeated = ?<br/>WHERE id = ?<br/>参数 [i, coord.id]
            DatabaseEngine-->>-SQLAlchemyORM: 返回 重复值更新成功 ✅
            SQLAlchemyORM-->>-CoordinateService: 返回 重新编号步骤完成
        end

        CoordinateService->>+DatabaseEngine: 4.8 调用 级联更新事务提交<br/>self._db.commit()
        DatabaseEngine-->>-CoordinateService: 返回 级联更新完成 ✅
    end

    %% 构建响应数据
    CoordinateService->>CoordinateService: 4.9 处理 构建响应对象<br/>return [{<br/>  "id" coordinate.id,<br/>  "table_id" coordinate.table_id,<br/>  "color" coordinate.color,<br/>  "position" coordinate.position,<br/>  "voc" coordinate.voc,<br/>  "repeated" coordinate.repeated<br/>}]

    CoordinateService-->>-CoordinateController: 返回 更新后的坐标数据<br/>{"coordinates" [updated_coordinate]}

    %% 响应构建阶段
    Note over CoordinateController, ResponseBuilder: 📤 阶段5 响应构建
    CoordinateController->>+ResponseBuilder: 5.1 调用 构建坐标响应<br/>return {"coordinates" updated_coordinates}
    ResponseBuilder->>ResponseBuilder: 5.2 处理 JSON序列化<br/>Coordinate对象 → JSON<br/>ID字段 → 字符串转换<br/>Content-Type application/json
    ResponseBuilder->>ResponseBuilder: 5.3 处理 响应结构<br/>{"coordinates" [...]}<br/>Status 200 OK
    ResponseBuilder-->>-CoordinateController: 返回 JSONResponse对象

    %% 资源清理和响应
    CoordinateController->>+DatabaseConfig: 5.4 调用 会话清理<br/>finally块自动执行 db.close()
    DatabaseConfig->>DatabaseConfig: 5.5 处理 SQLAlchemy会话清理<br/>session.close()<br/>连接池归还 engine.dispose()
    DatabaseConfig-->>-CoordinateController: 返回 资源释放完成 ✅

    CoordinateController-->>-Frontend: 5.6 返回 HTTP 200 OK响应<br/>Headers Content-Type application/json<br/>Body {<br/>  "coordinates" [{<br/>    "id" 456, "table_id" 1, "color" 0,<br/>    "position" "（100， 200）", "voc" "香蕉", "repeated" 2<br/>  }]<br/>}
</div>
        </div>
    </div>

    <script>

        // 添加交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为每个时序图添加全屏查看功能
            const mermaidContainers = document.querySelectorAll('.mermaid');
            mermaidContainers.forEach((container, index) => {
                container.style.cursor = 'pointer';
                container.title = '点击查看大图';

                container.addEventListener('click', function() {
                    // 创建全屏模态框
                    const modal = document.createElement('div');
                    modal.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0,0,0,0.9);
                        z-index: 9999;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        cursor: pointer;
                    `;

                    const clonedContainer = container.cloneNode(true);
                    clonedContainer.style.cssText = `
                        background: white;
                        padding: 20px;
                        border-radius: 10px;
                        max-width: 95%;
                        max-height: 95%;
                        overflow: auto;
                        cursor: default;
                    `;

                    modal.appendChild(clonedContainer);
                    document.body.appendChild(modal);

                    // 点击模态框背景关闭
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            document.body.removeChild(modal);
                        }
                    });

                    // ESC键关闭
                    const escHandler = function(e) {
                        if (e.key === 'Escape') {
                            document.body.removeChild(modal);
                            document.removeEventListener('keydown', escHandler);
                        }
                    };
                    document.addEventListener('keydown', escHandler);
                });
            });
        });
    </script>
</body>
</html>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 50,
                actorMargin: 80,
                width: 120,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 100,
                useMaxWidth: false,
                rightAngles: false,
                showSequenceNumbers: false,
                wrap: true,
                wrapPadding: 10
            }
        });
    </script>

    <!-- 技术文档说明 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-radius: 8px; font-family: 'Microsoft YaHei', sans-serif;">
        <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">📚 词汇管理系统时序图技术文档 v2.0</h2>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 20px;">

            <!-- 添加词汇流程说明 -->
            <div style="background: white; padding: 15px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 style="color: #27ae60; margin-top: 0;">🆕 添加词汇流程</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <p><strong>1. 请求验证阶段</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>FastAPI路由接收POST请求</li>
                        <li>Pydantic模型验证请求数据</li>
                        <li>依赖注入获取数据库会话</li>
                    </ul>

                    <p><strong>2. 文本处理算法</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li><code>split_text_by_comma()</code> 逗号分割</li>
                        <li><code>find_different_blocks()</code> 差异检测</li>
                        <li>Counter算法计算新增词汇</li>
                    </ul>

                    <p><strong>3. 智能编号机制</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>查询现有词汇最大编号</li>
                        <li>自动分配连续编号</li>
                        <li>支持重复词汇管理</li>
                    </ul>

                    <p><strong>4. 数据库操作</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>SQLAlchemy ORM批量插入</li>
                        <li>事务管理和回滚机制</li>
                        <li>数据一致性保证</li>
                    </ul>
                </div>
            </div>

            <!-- 删除词汇流程说明 -->
            <div style="background: white; padding: 15px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 style="color: #e74c3c; margin-top: 0;">🗑️ 删除词汇流程</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <p><strong>1. 删除策略判断</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>检查删除文本是否包含数字</li>
                        <li>精确删除 vs 模糊删除</li>
                        <li>删除范围确定</li>
                    </ul>

                    <p><strong>2. 复杂删除算法</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li><code>_handle_delete_with_digit()</code></li>
                        <li><code>_handle_delete_without_digit()</code></li>
                        <li>正则表达式匹配</li>
                    </ul>

                    <p><strong>3. 全局重编号</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>删除后编号空隙检测</li>
                        <li>批量重新编号算法</li>
                        <li>保持编号连续性</li>
                    </ul>

                    <p><strong>4. 级联更新</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>相关坐标数据更新</li>
                        <li>外键关系维护</li>
                        <li>数据完整性检查</li>
                    </ul>
                </div>
            </div>

            <!-- 坐标添加流程说明 -->
            <div style="background: white; padding: 15px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 style="color: #f39c12; margin-top: 0;">📍 坐标添加流程</h3>
                <div style="font-size: 14px; line-height: 1.6;">
                    <p><strong>1. 文件解析阶段</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>cor.txt文件上传处理</li>
                        <li>正则表达式解析坐标</li>
                        <li>数据格式验证</li>
                    </ul>

                    <p><strong>2. 坐标更新算法</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li><code>update_coordinate()</code> 核心方法</li>
                        <li>坐标值覆盖更新</li>
                        <li>词汇变更检测</li>
                    </ul>

                    <p><strong>3. 重复值计算</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>同表同色同词汇统计</li>
                        <li><code>_update_repeated_values()</code></li>
                        <li>自动编号分配</li>
                    </ul>

                    <p><strong>4. 级联重编号</strong></p>
                    <ul style="margin: 8px 0; padding-left: 20px;">
                        <li>旧词汇坐标重新编号</li>
                        <li>新词汇坐标编号分配</li>
                        <li>数据库事务提交</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 技术架构说明 -->
        <div style="margin-top: 30px; background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #8e44ad; margin-top: 0;">🏗️ 技术架构与核心算法</h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #2980b9;">📦 技术栈组件</h4>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><strong>FastAPI</strong>: 高性能异步Web框架</li>
                        <li><strong>Pydantic</strong>: 数据验证和序列化</li>
                        <li><strong>SQLAlchemy</strong>: ORM对象关系映射</li>
                        <li><strong>依赖注入</strong>: 数据库会话管理</li>
                        <li><strong>事务管理</strong>: ACID特性保证</li>
                    </ul>

                    <h4 style="color: #2980b9;">🔧 核心服务模块</h4>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><code>PhraseService</code>: 词汇业务逻辑</li>
                        <li><code>CoordinateService</code>: 坐标管理服务</li>
                        <li><code>TextProcessor</code>: 文本处理工具</li>
                        <li><code>DatabaseConfig</code>: 数据库配置</li>
                    </ul>
                </div>

                <div>
                    <h4 style="color: #c0392b;">⚡ 核心算法实现</h4>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><strong>文本分割算法</strong>: <code>split_text_by_comma()</code></li>
                        <li><strong>差异检测算法</strong>: <code>find_different_blocks()</code></li>
                        <li><strong>智能编号算法</strong>: 自动分配连续编号</li>
                        <li><strong>重编号算法</strong>: 删除后编号重整</li>
                        <li><strong>重复值计算</strong>: 同类坐标统计</li>
                    </ul>

                    <h4 style="color: #c0392b;">🎯 业务特性</h4>
                    <ul style="font-size: 14px; line-height: 1.6;">
                        <li><strong>批量操作</strong>: 支持多词汇同时处理</li>
                        <li><strong>级联更新</strong>: 关联数据自动维护</li>
                        <li><strong>数据一致性</strong>: 事务保证原子性</li>
                        <li><strong>错误处理</strong>: 完整的异常处理机制</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 数据流说明 -->
        <div style="margin-top: 20px; background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #16a085; margin-top: 0;">🔄 数据流转与状态管理</h3>

            <div style="font-size: 14px; line-height: 1.8;">
                <p><strong>📥 输入数据流</strong>: HTTP请求 → Pydantic验证 → 业务逻辑处理 → 数据库操作</p>
                <p><strong>🔄 处理数据流</strong>: 文本解析 → 算法计算 → 数据变更 → 级联更新</p>
                <p><strong>📤 输出数据流</strong>: 数据库查询 → 对象序列化 → JSON响应 → HTTP返回</p>
                <p><strong>⚠️ 异常处理流</strong>: 错误捕获 → 事务回滚 → 错误响应 → 资源清理</p>
            </div>
        </div>

        <!-- 核心算法详解 -->
        <div style="margin-top: 20px; background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #9b59b6; margin-top: 0;">🧮 核心算法详解</h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #e67e22;">📝 文本处理算法</h4>
                    <div style="font-size: 13px; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: 'Courier New', monospace;">
                        <p><strong>split_text_by_comma(text)</strong></p>
                        <p>• 智能逗号分割，处理中英文标点</p>
                        <p>• 去除空白字符和重复项</p>
                        <p>• 返回清洁的词汇列表</p>

                        <p style="margin-top: 15px;"><strong>find_different_blocks(old, new)</strong></p>
                        <p>• 使用Counter进行差异检测</p>
                        <p>• 计算新增和删除的词汇</p>
                        <p>• 支持批量操作优化</p>
                    </div>
                </div>

                <div>
                    <h4 style="color: #e67e22;">🔢 编号管理算法</h4>
                    <div style="font-size: 13px; background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: 'Courier New', monospace;">
                        <p><strong>智能编号分配</strong></p>
                        <p>• 查询现有最大编号</p>
                        <p>• 自动分配连续编号</p>
                        <p>• 支持重复词汇管理</p>

                        <p style="margin-top: 15px;"><strong>全局重编号算法</strong></p>
                        <p>• 删除后编号空隙检测</p>
                        <p>• 批量重新编号处理</p>
                        <p>• 保持编号连续性</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据库设计说明 -->
        <div style="margin-top: 20px; background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <h3 style="color: #34495e; margin-top: 0;">🗃️ 数据库设计与关系</h3>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4 style="color: #2c3e50;">📋 Phrase表结构</h4>
                    <div style="font-size: 13px; background: #ecf0f1; padding: 10px; border-radius: 4px;">
                        <p><strong>主要字段:</strong></p>
                        <ul style="margin: 5px 0; padding-left: 15px;">
                            <li><code>id</code>: 主键，自增</li>
                            <li><code>table_id</code>: 表标识</li>
                            <li><code>color</code>: 颜色分类</li>
                            <li><code>phrase</code>: 词汇内容</li>
                            <li><code>digit</code>: 编号字段</li>
                        </ul>
                        <p><strong>索引:</strong> (table_id, color, digit)</p>
                    </div>
                </div>

                <div>
                    <h4 style="color: #2c3e50;">📍 Coordinate表结构</h4>
                    <div style="font-size: 13px; background: #ecf0f1; padding: 10px; border-radius: 4px;">
                        <p><strong>主要字段:</strong></p>
                        <ul style="margin: 5px 0; padding-left: 15px;">
                            <li><code>id</code>: 主键，自增</li>
                            <li><code>table_id</code>: 表标识</li>
                            <li><code>color</code>: 颜色分类</li>
                            <li><code>position</code>: 坐标位置</li>
                            <li><code>voc</code>: 关联词汇</li>
                            <li><code>repeated</code>: 重复编号</li>
                        </ul>
                        <p><strong>外键:</strong> 与Phrase表关联</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 版本信息 -->
        <div style="margin-top: 20px; padding: 15px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 6px; text-align: center;">
            <p style="margin: 0; font-weight: bold;">📋 词汇管理系统时序图 v2.0 | 完整技术实现文档</p>
            <p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.9;">包含添加词汇、删除词汇、坐标管理三大核心业务流程的完整技术实现</p>
        </div>
    </div>

</body>
</html>
