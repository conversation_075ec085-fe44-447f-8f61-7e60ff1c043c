# Cube FastAPI 系统架构图表

## 📋 概述

本文档包含Cube FastAPI系统的三个核心架构图表，帮助开发团队理解系统结构、业务流程和数据关系。

---

## 🏗️ 1. 系统架构图

展示了Cube FastAPI系统的整体架构，包括各个模块之间的关系和数据流向。系统采用分层架构设计，包含路由层、服务层、数据访问层等。

```mermaid
graph TB
    subgraph "客户端层"
        Client[Web客户端/API客户端]
        Docs[API文档<br/>Swagger/ReDoc]
    end
    
    subgraph "API网关层"
        FastAPI[FastAPI应用<br/>main.py]
        CORS[CORS中间件]
        Exception[异常处理器]
    end
    
    subgraph "路由层 (Routers)"
        TableRouter[表格路由<br/>table.py]
        TextRouter[文本路由<br/>text_info.py]
        PhraseRouter[词汇路由<br/>phrase.py]
        CoordRouter[坐标路由<br/>coordinate.py]
    end
    
    subgraph "业务逻辑层 (Services)"
        TableService[表格服务<br/>table_service.py]
        TextService[文本服务<br/>text_info_service.py]
        PhraseService[词汇服务<br/>phrase_service.py]
        CoordService[坐标服务<br/>coordinate_service.py]
    end
    
    subgraph "数据访问层 (Models)"
        TableModel[表格模型<br/>table.py]
        TextModel[文本模型<br/>text_info.py]
        PhraseModel[词汇模型<br/>phrase.py]
        CoordModel[坐标模型<br/>coordinate.py]
    end
    
    subgraph "工具层 (Utils)"
        TextProcessor[文本处理器<br/>text_processor.py]
        IDGenerator[ID生成器<br/>id_generator.py]
        Exceptions[异常定义<br/>exceptions.py]
    end
    
    subgraph "数据层"
        SQLite[(SQLite数据库)]
        CorFile[cor.txt文件]
    end
    
    subgraph "配置层"
        Database[数据库配置<br/>database.py]
        Settings[应用设置<br/>settings.py]
    end
    
    Client --> FastAPI
    Docs --> FastAPI
    FastAPI --> CORS
    FastAPI --> Exception
    FastAPI --> TableRouter
    FastAPI --> TextRouter
    FastAPI --> PhraseRouter
    FastAPI --> CoordRouter
    
    TableRouter --> TableService
    TextRouter --> TextService
    PhraseRouter --> PhraseService
    CoordRouter --> CoordService
    
    TableService --> TableModel
    TextService --> TextModel
    PhraseService --> PhraseModel
    CoordService --> CoordModel
    
    TableModel --> SQLite
    TextModel --> SQLite
    PhraseModel --> SQLite
    CoordModel --> SQLite
    
    CoordService --> CorFile
    TextService --> TextProcessor
    PhraseService --> TextProcessor
    CoordService --> IDGenerator
    
    FastAPI --> Database
    FastAPI --> Settings
    
    classDef clientLayer fill:#e1f5fe
    classDef apiLayer fill:#f3e5f5
    classDef routerLayer fill:#e8f5e8
    classDef serviceLayer fill:#fff3e0
    classDef modelLayer fill:#fce4ec
    classDef utilLayer fill:#f1f8e9
    classDef dataLayer fill:#e0f2f1
    classDef configLayer fill:#fff8e1
    
    class Client,Docs clientLayer
    class FastAPI,CORS,Exception apiLayer
    class TableRouter,TextRouter,PhraseRouter,CoordRouter routerLayer
    class TableService,TextService,PhraseService,CoordService serviceLayer
    class TableModel,TextModel,PhraseModel,CoordModel modelLayer
    class TextProcessor,IDGenerator,Exceptions utilLayer
    class SQLite,CorFile dataLayer
    class Database,Settings configLayer
```

---

## 🔄 2. 业务流程图

展示了系统主要业务流程，包括文本处理、词汇分析、坐标管理等核心功能的处理流程。

```mermaid
graph TD
    Start([API请求开始]) --> Menu[选择功能模块]

    Menu --> TableMgmt[表格管理]
    Menu --> TextMgmt[文本管理]
    Menu --> PhraseMgmt[词汇管理]
    Menu --> CoordMgmt[坐标管理]

    subgraph "表格管理流程"
        TableMgmt --> TableOp{操作类型}
        TableOp -->|添加| TableAdd[添加表格]
        TableOp -->|查询| TableQuery[分页查询]
        TableOp -->|更新| TableUpdate[更新表格]
        TableOp -->|删除| TableDelete[删除表格]

        TableAdd --> TableValidate[数据验证]
        TableValidate -->|通过| TableSave[保存到数据库]
        TableValidate -->|失败| TableError[返回错误]
        TableSave --> TableSuccess[操作成功]
    end

    subgraph "文本管理流程"
        TextMgmt --> TextOp{操作类型}
        TextOp -->|查询| TextFind[查找所有文本信息]
        TextOp -->|更新| TextUpdateFlow[更新文本]

        TextFind --> TextResult[返回文本数据]
        TextUpdateFlow --> TextGetByColor[根据颜色获取文本]
        TextGetByColor --> TextProcess[文本处理]
        TextProcess --> TextSplit[按顿号分割]
        TextSplit --> TextSaveDB[保存到数据库]
        TextSaveDB --> TextSuccess[更新成功]
    end

    subgraph "词汇管理流程"
        PhraseMgmt --> PhraseOp{操作类型}
        PhraseOp -->|添加| PhraseAdd[添加词汇]
        PhraseOp -->|删除| PhraseDelete[删除词汇]
        PhraseOp -->|查询| PhraseQuery[查询词汇列表]

        PhraseAdd --> PhraseGetText[根据颜色获取文本]
        PhraseGetText --> PhraseGetAll[查询全局词汇表]
        PhraseGetAll --> PhraseProcess[词汇处理与计数]
        PhraseProcess --> PhraseSave[保存词汇]
        PhraseSave --> PhraseSuccess[操作成功]

        PhraseDelete --> PhraseGetText2[根据颜色获取文本]
        PhraseGetText2 --> PhraseAnalyze[分析删除的词汇块]
        PhraseAnalyze --> PhraseCheckDigit{检查是否有数字后缀}
        PhraseCheckDigit -->|无数字| PhraseSimpleDelete[简单删除+直接更新文本]
        PhraseCheckDigit -->|有数字| PhraseComplexDelete[复杂删除+批量更新]

        PhraseComplexDelete --> PhraseDeleteTarget[删除目标词汇]
        PhraseDeleteTarget --> PhraseUpdateCurrent[更新当前文本]
        PhraseUpdateCurrent --> PhraseGetRelated[获取需要更新的相关词汇]
        PhraseGetRelated --> PhraseCheckRelated{是否有相关词汇}
        PhraseCheckRelated -->|有| PhraseBatchUpdate[批量更新词汇类型和文本]
        PhraseCheckRelated -->|无| PhraseReturnCurrent[返回当前TextInfo]
        PhraseBatchUpdate --> PhraseReturnAll[返回所有更新的TextInfo]
        PhraseReturnCurrent --> PhraseComplexSuccess[复杂删除成功+TextInfo列表]
        PhraseReturnAll --> PhraseComplexSuccess

        PhraseSimpleDelete --> PhraseSimpleSuccess[简单删除成功]

        PhraseQuery --> PhraseGetByColor[根据颜色查询]
        PhraseGetByColor --> PhraseResult[返回词汇列表]
    end
    
    subgraph "坐标管理流程"
        CoordMgmt --> CoordOp{操作类型}
        CoordOp -->|批量导入| CoordBatch[批量添加坐标]
        CoordOp -->|查询| CoordFind[查询坐标]
        CoordOp -->|列表| CoordList[坐标词汇列表]
        CoordOp -->|更新| CoordUpdate[更新坐标]
        CoordOp -->|删除| CoordDelete[删除坐标]
        
        CoordBatch --> ReadCorFile[读取cor.txt文件]
        ReadCorFile --> ParseCoord[解析坐标数据]
        ParseCoord --> GenID[生成唯一ID]
        GenID --> CoordSave[批量保存]
        CoordSave --> CoordSuccess[导入成功]
    end
    
    TableSuccess --> End([API响应结束])
    TextSuccess --> End
    PhraseSimpleSuccess --> End
    PhraseComplexSuccess --> End
    CoordSuccess --> End
    TableError --> End
    PhraseError --> End
    
    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef process fill:#2196f3,stroke:#1565c0,color:#fff
    classDef decision fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef error fill:#f44336,stroke:#c62828,color:#fff
    classDef success fill:#8bc34a,stroke:#558b2f,color:#fff
    
    class Start,End startEnd
    class TableAdd,TableQuery,TableUpdate,TableDelete,TextFind,TextUpdateFlow,PhraseAdd,PhraseDelete,CoordBatch,CoordFind,CoordList,CoordUpdate,CoordDelete process
    class TableOp,TextOp,PhraseOp,CoordOp decision
    class TableError,PhraseError error
    class TableSuccess,TextSuccess,PhraseSimpleSuccess,PhraseComplexSuccess,CoordSuccess success
```

---

## 🗄️ 3. 数据库关系图

展示了系统中各个数据表之间的关系和字段结构，帮助理解数据模型设计。

```mermaid
erDiagram
    TABLE_INFO {
        bigint id PK "主键ID"
        varchar name "表格名称"
        datetime create_time "创建时间"
    }
    
    TEXT_INFO {
        bigint id PK "主键ID"
        int color "颜色标识(0-8)，唯一"
        varchar text "文本内容"
    }

    PHRASE {
        bigint id PK "主键ID"
        bigint textId FK "关联文本ID"
        varchar word "词汇内容"
        int type "词汇类型"
    }
    
    COORDINATE {
        bigint id PK "主键ID"
        bigint tableId FK "关联表格ID"
        int color "颜色标识"
        varchar position "坐标位置"
        varchar voc "词汇内容"
        int repeated "重复次数"
    }
    
    TABLE_INFO ||--o{ COORDINATE : "一对多"
    TEXT_INFO ||--o{ PHRASE : "一对多"
```

---

## 📊 图表说明

### 系统架构图特点
- **分层设计**: 清晰的分层架构，职责分离
- **模块化**: 每个功能模块独立，便于维护
- **可扩展**: 支持新功能模块的添加

### 业务流程图特点
- **完整流程**: 覆盖所有主要业务场景
- **错误处理**: 包含完善的错误处理机制
- **状态管理**: 清晰的状态转换逻辑

### 数据库关系图特点
- **规范化设计**: 遵循数据库设计规范
- **关系清晰**: 表间关系明确定义
- **扩展性好**: 支持业务扩展需求

---

## 🛠️ 技术栈

- **Web框架**: FastAPI - 现代、快速的Python Web框架
- **ORM**: SQLAlchemy - Python SQL工具包和ORM
- **数据库**: SQLite - 轻量级关系型数据库
- **数据验证**: Pydantic - 数据验证和设置管理
- **ASGI服务器**: Uvicorn - 高性能ASGI服务器
- **API文档**: Swagger UI / ReDoc - 自动生成API文档

---

## 🔧 最新优化记录

### 数据关系和业务流程修正 (v2.1.0)

**优化内容**:
1. **简化数据库查询**: 在`_handle_delete_without_digit`和`_handle_delete_with_digit`方法中，移除了不必要的TextInfo查询操作
2. **直接文本更新**: 使用传入的`new_text`直接更新数据库，避免复杂的字符串替换操作
3. **批量更新优化**: 在`_handle_delete_with_digit`中实现了批量更新相关TextInfo对象的功能
4. **优化执行顺序**: 将当前TextInfo的更新提前到获取相关词汇之前，确保数据一致性
5. **全局词汇查询**: 修改为查询所有TextInfo中的相关词汇，而不仅限于当前TextInfo
6. **确保排序**: 保证phrase列表按type从小到大排序，确保更新操作的正确性
7. **差异化返回结果**: 实现了两种不同的返回格式，简单删除返回成功消息，复杂删除返回更新的TextInfo列表
8. **词汇添加优化**: 词汇添加接口现在返回完整的TextInfo对象，支持前端精确更新
9. **TextInfo更新优化**: TextInfo更新接口支持通过ID精确更新，返回完整对象
10. **数据关系修正**: 明确了TextInfo与Table是独立的业务模块，无直接关联关系
11. **提升性能**: 减少了数据库查询次数，提高了操作的执行效率

**影响的组件**:
- `PhraseService._handle_delete_without_digit()` - 简化逻辑，提升性能
- `PhraseService._handle_delete_with_digit()` - 重构批量更新逻辑，支持多TextInfo更新，返回更新的TextInfo列表
- `PhraseService.delete_phrase()` - 实现差异化返回结果，根据删除类型返回不同格式的数据
- `PhraseService.add_phrase()` - 返回完整的TextInfo对象，支持前端精确更新
- `TextInfoService.update_text_info_by_data()` - 新增通过ID精确更新的方法
- `TextInfoUpdate` Schema - 添加ID字段，支持完整对象更新
- `TableService` - 移除了与TextInfo/Phrase的错误关联逻辑

**业务流程变化**:
- 词汇删除流程中的"简单删除"分支现在更加高效，返回简单的成功消息
- "复杂删除"分支增加了批量更新相关词汇和文本的功能，返回所有更新的TextInfo对象
- 词汇添加流程现在始终返回完整的TextInfo对象，前端可以通过ID精确更新
- TextInfo更新流程支持通过ID精确定位，避免了通过颜色查找的不确定性
- 表格管理流程与文本/词汇管理完全独立，避免了错误的业务关联
- 前端可以根据返回结果的格式判断操作类型，并相应地更新界面
- 减少了数据库交互次数，提升了用户体验
- 支持跨TextInfo的词汇类型更新
- 提供了更完整的数据一致性保证

---

## 📞 联系信息

如有架构相关问题，请联系开发团队。

**文档版本**: 2.1.0
**最后更新**: 2025-07-24
