<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube FastAPI 系统架构图表</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 300;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .nav {
            background: #f8f9fa;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            position: sticky;
            top: 140px;
            z-index: 99;
        }
        
        .nav ul {
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 2rem;
        }
        
        .nav a {
            color: #495057;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .nav a:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .content {
            padding: 3rem 2rem;
        }
        
        .diagram-section {
            margin-bottom: 5rem;
            scroll-margin-top: 220px;
        }
        
        .diagram-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 4px solid #667eea;
            display: inline-block;
            position: relative;
        }
        
        .diagram-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 50%;
            height: 4px;
            background: #764ba2;
        }
        
        .diagram-description {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.8;
            font-size: 1.2rem;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .mermaid {
            background: #fafafa;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .tech-stack {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 2rem;
            border-radius: 15px;
            margin: 3rem 0;
            border: 1px solid #dee2e6;
        }
        
        .tech-stack h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }
        
        .tech-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .tech-item {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 5px solid #667eea;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .tech-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .tech-item strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .optimization-record {
            background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
            padding: 2rem;
            border-radius: 15px;
            margin: 3rem 0;
            border: 1px solid #c8e6c9;
            border-left: 5px solid #4caf50;
        }

        .optimization-record h3 {
            color: #2e7d32;
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
        }

        .optimization-content h4 {
            color: #388e3c;
            margin: 1.5rem 0 1rem 0;
            font-size: 1.3rem;
        }

        .optimization-content ul {
            margin: 1rem 0;
            padding-left: 1.5rem;
        }

        .optimization-content li {
            margin: 0.8rem 0;
            line-height: 1.6;
        }

        .optimization-content code {
            background: #f5f5f5;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #d32f2f;
        }
        
        .tech-item span {
            color: #666;
            font-size: 0.95rem;
        }
        
        .features {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            border: 1px solid #2196f3;
        }
        
        .features h4 {
            color: #1976d2;
            margin-bottom: 1rem;
            font-size: 1.4rem;
        }
        
        .features ul {
            list-style: none;
            padding-left: 0;
        }
        
        .features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 2rem;
        }
        
        .features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #4caf50;
            font-weight: bold;
            font-size: 1.2rem;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 3rem 2rem;
            margin-top: 4rem;
        }
        
        .footer p {
            margin: 0.5rem 0;
            font-size: 1.1rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 2rem 1rem;
            }
            
            .nav {
                padding: 1rem;
            }
            
            .nav ul {
                flex-direction: column;
                gap: 1rem;
            }
            
            .diagram-title {
                font-size: 2rem;
            }
        }
        
        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 12px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 6px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        /* 动画效果 */
        .diagram-section {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.8s ease forwards;
        }
        
        .diagram-section:nth-child(1) { animation-delay: 0.2s; }
        .diagram-section:nth-child(2) { animation-delay: 0.4s; }
        .diagram-section:nth-child(3) { animation-delay: 0.6s; }
        
        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🏗️ Cube FastAPI 系统架构图表</h1>
            <p>全面展示系统架构、业务流程和数据关系的可视化文档</p>
        </header>
        
        <nav class="nav">
            <ul>
                <li><a href="#architecture">🏗️ 系统架构图</a></li>
                <li><a href="#workflow">🔄 业务流程图</a></li>
                <li><a href="#database">🗄️ 数据库关系图</a></li>
                <li><a href="#optimization">🔧 最新优化</a></li>
            </ul>
        </nav>
        
        <main class="content">
            <!-- 系统架构图 -->
            <section id="architecture" class="diagram-section">
                <h2 class="diagram-title">🏗️ 系统架构图</h2>
                <div class="diagram-description">
                    展示了Cube FastAPI系统的整体架构，包括各个模块之间的关系和数据流向。
                    系统采用分层架构设计，包含客户端层、API网关层、路由层、业务逻辑层、数据访问层等。
                </div>
                <div class="mermaid">
graph TB
    subgraph "客户端层"
        Client[Web客户端/API客户端]
        Docs[API文档<br/>Swagger/ReDoc]
    end
    
    subgraph "API网关层"
        FastAPI[FastAPI应用<br/>main.py]
        CORS[CORS中间件]
        Exception[异常处理器]
    end
    
    subgraph "路由层 (Routers)"
        TableRouter[表格路由<br/>table.py]
        TextRouter[文本路由<br/>text_info.py]
        PhraseRouter[词汇路由<br/>phrase.py]
        CoordRouter[坐标路由<br/>coordinate.py]
    end
    
    subgraph "业务逻辑层 (Services)"
        TableService[表格服务<br/>table_service.py]
        TextService[文本服务<br/>text_info_service.py]
        PhraseService[词汇服务<br/>phrase_service.py]
        CoordService[坐标服务<br/>coordinate_service.py]
    end
    
    subgraph "数据访问层 (Models)"
        TableModel[表格模型<br/>table.py]
        TextModel[文本模型<br/>text_info.py]
        PhraseModel[词汇模型<br/>phrase.py]
        CoordModel[坐标模型<br/>coordinate.py]
    end
    
    subgraph "工具层 (Utils)"
        TextProcessor[文本处理器<br/>text_processor.py]
        IDGenerator[ID生成器<br/>id_generator.py]
        Exceptions[异常定义<br/>exceptions.py]
    end
    
    subgraph "数据层"
        SQLite[(SQLite数据库)]
        CorFile[cor.txt文件]
    end
    
    subgraph "配置层"
        Database[数据库配置<br/>database.py]
        Settings[应用设置<br/>settings.py]
    end
    
    Client --> FastAPI
    Docs --> FastAPI
    FastAPI --> CORS
    FastAPI --> Exception
    FastAPI --> TableRouter
    FastAPI --> TextRouter
    FastAPI --> PhraseRouter
    FastAPI --> CoordRouter
    
    TableRouter --> TableService
    TextRouter --> TextService
    PhraseRouter --> PhraseService
    CoordRouter --> CoordService
    
    TableService --> TableModel
    TextService --> TextModel
    PhraseService --> PhraseModel
    CoordService --> CoordModel
    
    TableModel --> SQLite
    TextModel --> SQLite
    PhraseModel --> SQLite
    CoordModel --> SQLite
    
    CoordService --> CorFile
    TextService --> TextProcessor
    PhraseService --> TextProcessor
    CoordService --> IDGenerator
    
    FastAPI --> Database
    FastAPI --> Settings
    
    classDef clientLayer fill:#e1f5fe
    classDef apiLayer fill:#f3e5f5
    classDef routerLayer fill:#e8f5e8
    classDef serviceLayer fill:#fff3e0
    classDef modelLayer fill:#fce4ec
    classDef utilLayer fill:#f1f8e9
    classDef dataLayer fill:#e0f2f1
    classDef configLayer fill:#fff8e1
    
    class Client,Docs clientLayer
    class FastAPI,CORS,Exception apiLayer
    class TableRouter,TextRouter,PhraseRouter,CoordRouter routerLayer
    class TableService,TextService,PhraseService,CoordService serviceLayer
    class TableModel,TextModel,PhraseModel,CoordModel modelLayer
    class TextProcessor,IDGenerator,Exceptions utilLayer
    class SQLite,CorFile dataLayer
    class Database,Settings configLayer
                </div>
                
                <div class="features">
                    <h4>系统架构图特点</h4>
                    <ul>
                        <li><strong>分层设计</strong>: 清晰的分层架构，职责分离</li>
                        <li><strong>模块化</strong>: 每个功能模块独立，便于维护</li>
                        <li><strong>可扩展</strong>: 支持新功能模块的添加</li>
                    </ul>
                </div>
            </section>

            <!-- 业务流程图 -->
            <section id="workflow" class="diagram-section">
                <h2 class="diagram-title">🔄 业务流程图</h2>
                <div class="diagram-description">
                    展示了系统主要业务流程，包括文本处理、词汇分析、坐标管理等核心功能的处理流程。
                    涵盖了完整的业务操作链路和错误处理机制。
                </div>
                <div class="mermaid">
graph TD
    Start([API请求开始]) --> Menu[选择功能模块]

    Menu --> TableMgmt[表格管理]
    Menu --> TextMgmt[文本管理]
    Menu --> PhraseMgmt[词汇管理]
    Menu --> CoordMgmt[坐标管理]

    subgraph "表格管理流程"
        TableMgmt --> TableOp{操作类型}
        TableOp -->|添加| TableAdd[添加表格]
        TableOp -->|查询| TableQuery[分页查询]
        TableOp -->|更新| TableUpdate[更新表格]
        TableOp -->|删除| TableDelete[删除表格]

        TableAdd --> TableValidate[数据验证]
        TableValidate -->|通过| TableSave[保存到数据库]
        TableValidate -->|失败| TableError[返回错误]
        TableSave --> TableSuccess[操作成功]
    end

    subgraph "文本管理流程"
        TextMgmt --> TextOp{操作类型}
        TextOp -->|查询| TextFind[查找文本信息]
        TextOp -->|更新| TextUpdateFlow[更新文本]

        TextFind --> TextResult[返回文本数据]
        TextUpdateFlow --> TextProcess[文本处理]
        TextProcess --> TextSplit[按顿号分割]
        TextSplit --> TextDiff[找出差异块]
        TextDiff --> TextIndex[获取索引映射]
        TextIndex --> TextSaveDB[保存到数据库]
        TextSaveDB --> TextSuccess[更新成功]
    end

    subgraph "词汇管理流程"
        PhraseMgmt --> PhraseOp{操作类型}
        PhraseOp -->|添加| PhraseAdd[添加词汇]
        PhraseOp -->|删除| PhraseDelete[删除词汇]
        PhraseOp -->|查询| PhraseQuery[查询词汇列表]

        PhraseAdd --> PhraseGetText[根据颜色获取文本]
        PhraseGetText --> PhraseProcess[词汇处理]
        PhraseProcess --> PhraseSave[保存词汇]
        PhraseSave --> PhraseSuccess[操作成功]

        PhraseDelete --> PhraseGetText2[根据颜色获取文本]
        PhraseGetText2 --> PhraseAnalyze[分析删除的词汇块]
        PhraseAnalyze --> PhraseCheckDigit{检查是否有数字后缀}
        PhraseCheckDigit -->|无数字| PhraseSimpleDelete[简单删除+直接更新文本]
        PhraseCheckDigit -->|有数字| PhraseComplexDelete[复杂删除+批量更新]

        PhraseComplexDelete --> PhraseDeleteTarget[删除目标词汇]
        PhraseDeleteTarget --> PhraseUpdateCurrent[更新当前文本]
        PhraseUpdateCurrent --> PhraseGetRelated[获取需要更新的相关词汇]
        PhraseGetRelated --> PhraseCheckRelated{是否有相关词汇}
        PhraseCheckRelated -->|有| PhraseBatchUpdate[批量更新词汇类型和文本]
        PhraseCheckRelated -->|无| PhraseReturnCurrent[返回当前TextInfo]
        PhraseBatchUpdate --> PhraseReturnAll[返回所有更新的TextInfo]
        PhraseReturnCurrent --> PhraseComplexSuccess[复杂删除成功+TextInfo列表]
        PhraseReturnAll --> PhraseComplexSuccess

        PhraseSimpleDelete --> PhraseSimpleSuccess[简单删除成功]

        PhraseQuery --> PhraseGetByColor[根据颜色查询]
        PhraseGetByColor --> PhraseResult[返回词汇列表]
    end

    subgraph "坐标管理流程"
        CoordMgmt --> CoordOp{操作类型}
        CoordOp -->|批量导入| CoordBatch[批量添加坐标]
        CoordOp -->|查询| CoordFind[查询坐标]
        CoordOp -->|列表| CoordList[坐标词汇列表]
        CoordOp -->|更新| CoordUpdate[更新坐标]
        CoordOp -->|删除| CoordDelete[删除坐标]

        CoordBatch --> ReadCorFile[读取cor.txt文件]
        ReadCorFile --> ParseCoord[解析坐标数据]
        ParseCoord --> GenID[生成唯一ID]
        GenID --> CoordSave[批量保存]
        CoordSave --> CoordSuccess[导入成功]
    end

    TableSuccess --> End([API响应结束])
    TextSuccess --> End
    PhraseSimpleSuccess --> End
    PhraseComplexSuccess --> End
    CoordSuccess --> End
    TableError --> End
    PhraseError --> End

    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef process fill:#2196f3,stroke:#1565c0,color:#fff
    classDef decision fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef error fill:#f44336,stroke:#c62828,color:#fff
    classDef success fill:#8bc34a,stroke:#558b2f,color:#fff

    class Start,End startEnd
    class TableAdd,TableQuery,TableUpdate,TableDelete,TextFind,TextUpdateFlow,PhraseAdd,PhraseDelete,CoordBatch,CoordFind,CoordList,CoordUpdate,CoordDelete process
    class TableOp,TextOp,PhraseOp,CoordOp decision
    class TableError,PhraseError error
    class TableSuccess,TextSuccess,PhraseSimpleSuccess,PhraseComplexSuccess,CoordSuccess success
                </div>

                <div class="features">
                    <h4>业务流程图特点</h4>
                    <ul>
                        <li><strong>完整流程</strong>: 覆盖所有主要业务场景</li>
                        <li><strong>错误处理</strong>: 包含完善的错误处理机制</li>
                        <li><strong>状态管理</strong>: 清晰的状态转换逻辑</li>
                    </ul>
                </div>
            </section>

            <!-- 数据库关系图 -->
            <section id="database" class="diagram-section">
                <h2 class="diagram-title">🗄️ 数据库关系图</h2>
                <div class="diagram-description">
                    展示了系统中各个数据表之间的关系和字段结构，帮助理解数据模型设计。
                    采用规范化设计，确保数据一致性和完整性。
                </div>
                <div class="mermaid">
erDiagram
    TABLE_INFO {
        bigint id PK "主键ID"
        varchar name "表格名称"
        datetime create_time "创建时间"
    }

    TEXT_INFO {
        bigint id PK "主键ID"
        int color "颜色标识(0-8)，唯一"
        varchar text "文本内容"
    }

    PHRASE {
        bigint id PK "主键ID"
        bigint textId FK "关联文本ID"
        varchar word "词汇内容"
        int type "词汇类型"
    }

    COORDINATE {
        bigint id PK "主键ID"
        bigint tableId FK "关联表格ID"
        int color "颜色标识"
        varchar position "坐标位置"
        varchar voc "词汇内容"
        int repeated "重复次数"
    }

    TABLE_INFO ||--o{ COORDINATE : "一对多"
    TEXT_INFO ||--o{ PHRASE : "一对多"
                </div>

                <div class="features">
                    <h4>数据库关系图特点</h4>
                    <ul>
                        <li><strong>规范化设计</strong>: 遵循数据库设计规范</li>
                        <li><strong>关系清晰</strong>: 表间关系明确定义</li>
                        <li><strong>扩展性好</strong>: 支持业务扩展需求</li>
                    </ul>
                </div>
            </section>

            <!-- 技术栈信息 -->
            <div class="tech-stack">
                <h3>🛠️ 技术栈详情</h3>
                <div class="tech-list">
                    <div class="tech-item">
                        <strong>Web框架</strong>
                        <span>FastAPI - 现代、快速的Python Web框架</span>
                    </div>
                    <div class="tech-item">
                        <strong>ORM</strong>
                        <span>SQLAlchemy - Python SQL工具包和ORM</span>
                    </div>
                    <div class="tech-item">
                        <strong>数据库</strong>
                        <span>SQLite - 轻量级关系型数据库</span>
                    </div>
                    <div class="tech-item">
                        <strong>数据验证</strong>
                        <span>Pydantic - 数据验证和设置管理</span>
                    </div>
                    <div class="tech-item">
                        <strong>ASGI服务器</strong>
                        <span>Uvicorn - 高性能ASGI服务器</span>
                    </div>
                    <div class="tech-item">
                        <strong>API文档</strong>
                        <span>Swagger UI / ReDoc - 自动生成API文档</span>
                    </div>
                </div>
            </div>

            <!-- 优化记录 -->
            <div class="diagram-section" id="optimization">
                <h2 class="diagram-title">🔧 最新优化记录</h2>

                <div class="optimization-record">
                    <h3>数据关系和业务流程修正 (v2.1.0)</h3>

                    <div class="optimization-content">
                        <h4>优化内容:</h4>
                        <ul>
                            <li><strong>简化数据库查询</strong>: 在<code>_handle_delete_without_digit</code>和<code>_handle_delete_with_digit</code>方法中，移除了不必要的TextInfo查询操作</li>
                            <li><strong>直接文本更新</strong>: 使用传入的<code>new_text</code>直接更新数据库，避免复杂的字符串替换操作</li>
                            <li><strong>批量更新优化</strong>: 在<code>_handle_delete_with_digit</code>中实现了批量更新相关TextInfo对象的功能</li>
                            <li><strong>优化执行顺序</strong>: 将当前TextInfo的更新提前到获取相关词汇之前，确保数据一致性</li>
                            <li><strong>全局词汇查询</strong>: 修改为查询所有TextInfo中的相关词汇，而不仅限于当前TextInfo</li>
                            <li><strong>确保排序</strong>: 保证phrase列表按type从小到大排序，确保更新操作的正确性</li>
                            <li><strong>差异化返回结果</strong>: 实现了两种不同的返回格式，简单删除返回成功消息，复杂删除返回更新的TextInfo列表</li>
                            <li><strong>词汇添加优化</strong>: 词汇添加接口现在返回完整的TextInfo对象，支持前端精确更新</li>
                            <li><strong>TextInfo更新优化</strong>: TextInfo更新接口支持通过ID精确更新，返回完整对象</li>
                            <li><strong>数据关系修正</strong>: 明确了TextInfo与Table是独立的业务模块，无直接关联关系</li>
                            <li><strong>提升性能</strong>: 减少了数据库查询次数，提高了操作的执行效率</li>
                        </ul>

                        <h4>影响的组件:</h4>
                        <ul>
                            <li><code>PhraseService._handle_delete_without_digit()</code> - 简化逻辑，提升性能</li>
                            <li><code>PhraseService._handle_delete_with_digit()</code> - 重构批量更新逻辑，支持多TextInfo更新，返回更新的TextInfo列表</li>
                            <li><code>PhraseService.delete_phrase()</code> - 实现差异化返回结果，根据删除类型返回不同格式的数据</li>
                            <li><code>PhraseService.add_phrase()</code> - 返回完整的TextInfo对象，支持前端精确更新</li>
                            <li><code>TextInfoService.update_text_info_by_data()</code> - 新增通过ID精确更新的方法</li>
                            <li><code>TextInfoUpdate</code> Schema - 添加ID字段，支持完整对象更新</li>
                            <li><code>TableService</code> - 移除了与TextInfo/Phrase的错误关联逻辑</li>
                        </ul>

                        <h4>业务流程变化:</h4>
                        <ul>
                            <li>词汇删除流程中的"简单删除"分支现在更加高效，返回简单的成功消息</li>
                            <li>"复杂删除"分支增加了批量更新相关词汇和文本的功能，返回所有更新的TextInfo对象</li>
                            <li>词汇添加流程现在始终返回完整的TextInfo对象，前端可以通过ID精确更新</li>
                            <li>TextInfo更新流程支持通过ID精确定位，避免了通过颜色查找的不确定性</li>
                            <li>表格管理流程与文本/词汇管理完全独立，避免了错误的业务关联</li>
                            <li>前端可以根据返回结果的格式判断操作类型，并相应地更新界面</li>
                            <li>减少了数据库交互次数，提升了用户体验</li>
                            <li>支持跨TextInfo的词汇类型更新</li>
                            <li>提供了更完整的数据一致性保证</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2025 Cube FastAPI 系统架构文档</p>
            <p>文档版本: 2.1.0 | 最后更新: 2025-07-24</p>
        </footer>
    </div>
    
    <script>
        // 初始化Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
        
        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
