<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>delete_phrase 操作详细数据流分析</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #34495e;
            margin-top: 40px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
        }
        
        h4 {
            color: #7f8c8d;
            margin-top: 25px;
            margin-bottom: 10px;
        }
        
        .overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            font-size: 18px;
            font-weight: 500;
        }
        
        .legend {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 15px 5px 0;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }
        
        .legend-input { background: #e1f5fe; color: #01579b; }
        .legend-process { background: #f3e5f5; color: #4a148c; }
        .legend-database { background: #fff3e0; color: #e65100; }
        .legend-output { background: #e8f5e8; color: #1b5e20; }
        .legend-validation { background: #fce4ec; color: #880e4f; }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.4;
        }
        
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 14px;
            color: #d73a49;
        }
        
        .data-flow {
            background: #e8f4fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 6px 6px 0;
        }
        
        .data-flow h5 {
            margin: 0 0 10px 0;
            color: #1976d2;
            font-weight: 600;
        }
        
        .node-section {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .node-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .node-content {
            padding: 20px;
        }
        
        .mermaid-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
        }
        
        .summary-card h4 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #6c757d;
            padding-bottom: 8px;
        }
        
        .summary-card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .summary-card li {
            margin: 8px 0;
            color: #6c757d;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        
        .toc {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        
        .toc li {
            margin: 8px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .toc li:before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #3498db;
        }
        
        .toc a {
            color: #2c3e50;
            text-decoration: none;
        }
        
        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 delete_phrase 操作详细数据流分析</h1>
        
        <div class="overview">
            Frontend Request → FastAPI Router → Pydantic Validation → Business Logic → SQLAlchemy ORM → Database
        </div>
        
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#architecture-diagram">数据流程图</a></li>
                <li><a href="#detailed-architecture">详细技术架构图</a></li>
                <li><a href="#business-logic-architecture">业务逻辑详细架构图</a></li>
                <li><a href="#comprehensive-architecture">🌟 综合融合架构图 v1.0</a></li>
                <li><a href="#comprehensive-architecture-v2">🚀 综合融合架构图 v2.0</a></li>
                <li><a href="#comprehensive-architecture-v3">⚡ 综合融合架构图 v3.0</a></li>
                <li><a href="#comprehensive-architecture-v4">🎯 综合融合架构图 v4.0</a></li>
                <li><a href="#v5-0">🧠 信息分层架构图 v5.0</a></li>
                <li><a href="#node-analysis">节点详细分析</a></li>
                <li><a href="#technical-summary">技术要点总结</a></li>
                <li><a href="#conclusion">总结</a></li>
            </ul>
        </div>
        
        <h2 id="overview">📖 概述</h2>
        <p>本文档详细分析了中文词汇管理系统中 <code>delete_phrase</code> 操作的完整数据流，从 FastAPI 接口接收请求到数据库操作完成的每个节点的具体实现细节。</p>
        
        <div class="highlight">
            <strong>🎯 分析目标：</strong> 深入理解 FastAPI + SQLAlchemy + Pydantic 技术栈的数据处理机制，为系统优化和维护提供技术基础。
        </div>
    </div>

    <div class="container">
        <h2 id="architecture-diagram">🏗️ 详细架构图</h2>
        
        <div class="legend">
            <h4>图例说明：</h4>
            <span class="legend-item legend-input">🔵 输入数据层</span>
            <span class="legend-item legend-validation">🔴 验证层</span>
            <span class="legend-item legend-process">🟣 数据处理层</span>
            <span class="legend-item legend-database">🟠 数据库层</span>
            <span class="legend-item legend-output">🟢 输出数据层</span>
        </div>
        
        <div class="mermaid-container">
            <div class="mermaid">
graph TD
    A[Frontend Request] -->|HTTP DELETE| B[FastAPI Application]
    B --> C{Route Matching}
    C -->|/phrase/delete| D[HTTP Request Parser]
    D --> E[JSON Parser]
    E --> F[Pydantic Validator]
    F --> G{Validation Process}
    G -->|Field Check| H[Type Conversion]
    H -->|Range Check| I[Length Validation]
    I -->|All Valid| J[TextInfoColorUpdate Object]
    B --> K[Dependency Injection]
    K -->|get_db| L[Database Session]
    J --> M[PhraseService Instance]
    L --> M
    M --> N[Business Logic Layer]
    N --> O[Query TextInfo by Color]
    O --> P[Database Query]
    P --> Q[Text Difference Analysis]
    Q --> R{Deletion Type}
    R -->|Simple Delete| S[Update Current TextInfo Only]
    R -->|Complex Delete| T[Global Renumbering Process]
    T --> U[Find Phrases to Renumber]
    U --> V[Batch Update Phrases]
    V --> W[Update Related TextInfos]
    W --> X[Transaction Commit]
    S --> Y[Simple Transaction Commit]
    P --> Z[SQLAlchemy ORM]
    Z --> AA[Model Mapping]
    AA --> BB[Object State Management]
    BB --> CC[Session Tracking]
    V --> DD[Phrase Model Operations]
    DD --> EE[Bulk Operations]
    EE --> FF[Change Tracking]
    X --> GG[Database Transaction Log]
    Y --> GG
    GG --> HH[SQL Execution Engine]
    HH --> II{Transaction Operations}
    II -->|DELETE| JJ[phrase表删除1行]
    II -->|UPDATE| KK[text_info表更新2行]
    II -->|UPDATE| LL[phrase表更新2行]
    JJ --> MM[COMMIT Transaction]
    KK --> MM
    LL --> MM
    MM --> NN[Response Builder]
    NN --> OO{Response Type}
    OO -->|Simple| PP[Simple Response]
    OO -->|Complex| QQ[Complex Response]
    PP --> RR[JSON Serializer]
    QQ --> RR
    RR --> SS[HTTP Response Builder]
    SS --> TT[Response Headers]
    TT --> UU[HTTP Response]
    UU --> VV[Frontend Response]

    classDef inputData fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processData fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef outputData fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef databaseData fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef validationData fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A,E,J inputData
    class F,G,H,I validationData
    class M,N,Q,R,T,U processData
    class P,Z,AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM databaseData
    class NN,OO,PP,QQ,RR,SS,TT,UU,VV outputData
            </div>
        </div>
        
        <div class="data-flow">
            <h5>🔄 关键数据流转过程：</h5>
            <ol>
                <li><strong>HTTP → JSON → Pydantic对象</strong>：请求数据的解析和验证</li>
                <li><strong>业务逻辑分支</strong>：简单删除 vs 复杂删除（全局重新编号）</li>
                <li><strong>数据库操作</strong>：查询 → 删除 → 批量更新 → 事务提交</li>
                <li><strong>响应构建</strong>：根据操作类型返回不同格式的JSON响应</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <h2 id="detailed-architecture">🏛️ 详细技术架构图</h2>

        <div class="legend">
            <h4>架构图说明：</h4>
            <p>以下架构图展示了系统的技术层次结构，包含具体的类名、方法名、数据结构和处理细节：</p>
            <span class="legend-item legend-input">🔵 前端接口层</span>
            <span class="legend-item legend-validation">🔴 数据验证层</span>
            <span class="legend-item legend-process">🟣 业务逻辑层</span>
            <span class="legend-item legend-database">🟠 数据持久层</span>
            <span class="legend-item legend-output">🟢 响应处理层</span>
        </div>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🌐 Frontend Interface Layer"
        A1["HTTP Request<br/>DELETE /phrase/delete<br/>Content-Type: application/json<br/>Body: {color:0, text:'苹果、香蕉、苹果1、橙子、'}"]
        A2["FastAPI Application<br/>app = FastAPI()<br/>CORS Middleware<br/>Exception Handlers"]
        A3["APIRouter<br/>router = APIRouter(prefix='/phrase')<br/>@router.delete('/delete')"]
    end

    subgraph "🔴 Data Validation Layer"
        B1["Pydantic Model<br/>TextInfoColorUpdate<br/>color: int = Field(ge=0, le=8)<br/>text: str = Field(max_length=1000)"]
        B2["Validation Process<br/>- Field existence check<br/>- Type conversion<br/>- Range validation<br/>- Length validation"]
        B3["Validated Object<br/>TextInfoColorUpdate(<br/>  color=0,<br/>  text='苹果、香蕉、苹果1、橙子、'<br/>)"]
    end

    subgraph "🟣 Business Logic Layer"
        C1["PhraseService<br/>class PhraseService:<br/>  def __init__(self, db: Session)<br/>  async def delete_phrase(color, text)"]
        C2["Text Processing<br/>TextProcessor.split_text_by_comma()<br/>TextProcessor.find_different_blocks()<br/>old_text vs new_text analysis"]
        C3["Business Rules<br/>if has_digit_suffix:<br/>  global_renumbering()<br/>else:<br/>  simple_update()"]
    end

    subgraph "🟠 Data Persistence Layer"
        D1["SQLAlchemy Models<br/>TextInfo(Base):<br/>  id: BigInteger<br/>  color: Integer(unique=True)<br/>  text: String<br/>Phrase(Base):<br/>  id: BigInteger<br/>  text_id: BigInteger<br/>  word: String<br/>  type: Integer"]
        D2["ORM Operations<br/>db.query(TextInfo).filter(color==0).first()<br/>db.query(Phrase).filter(word=='苹果', type>2)<br/>db.bulk_update_mappings(TextInfo, mappings)<br/>db.commit()"]
        D3["Database Transactions<br/>BEGIN<br/>DELETE FROM phrase WHERE...<br/>UPDATE text_info SET...<br/>UPDATE phrase SET type=type-1...<br/>COMMIT"]
    end

    subgraph "🟢 Response Processing Layer"
        E1["Response Builder<br/>if updated_text_infos:<br/>  complex_response()<br/>else:<br/>  simple_response()"]
        E2["JSON Serialization<br/>json.dumps(response_data,<br/>  ensure_ascii=False,<br/>  separators=(',', ':'))<br/>ID conversion: str(id)"]
        E3["HTTP Response<br/>JSONResponse(<br/>  content=response_data,<br/>  status_code=200,<br/>  headers={'Content-Type': 'application/json'}<br/>)"]
    end

    subgraph "🗄️ Database Layer"
        F1["PostgreSQL/MySQL<br/>Tables:<br/>- text_info (id, color, text)<br/>- phrase (id, text_id, word, type)<br/>Indexes: color(unique), text_id, word+type"]
        F2["SQL Execution<br/>Query Plan Optimization<br/>Transaction Isolation<br/>Connection Pooling<br/>Performance: ~50ms"]
    end

    subgraph "🔧 Infrastructure Layer"
        G1["Dependency Injection<br/>get_db() -> Session<br/>SessionLocal = sessionmaker()<br/>Connection lifecycle management"]
        G2["Error Handling<br/>BusinessException<br/>HTTPException<br/>Pydantic ValidationError<br/>Database rollback"]
    end

    A1 --> A2
    A2 --> A3
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> C1
    G1 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> F1
    F1 --> F2
    F2 --> E1
    E1 --> E2
    E2 --> E3
    G2 -.-> C1
    G2 -.-> D2
    G2 -.-> E1

    classDef frontendStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef validationStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef responseStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef dbStyle fill:#fff8e1,stroke:#ffa000,stroke-width:2px
    classDef infraStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class A1,A2,A3 frontendStyle
    class B1,B2,B3 validationStyle
    class C1,C2,C3 businessStyle
    class D1,D2,D3 dataStyle
    class E1,E2,E3 responseStyle
    class F1,F2 dbStyle
    class G1,G2 infraStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>🏗️ 架构特点：</h5>
            <ul>
                <li><strong>分层架构</strong>：清晰的7层架构设计，职责分离</li>
                <li><strong>技术细节</strong>：包含具体的类名、方法名、SQL语句</li>
                <li><strong>数据流向</strong>：从HTTP请求到数据库再到响应的完整路径</li>
                <li><strong>错误处理</strong>：跨层的异常处理机制</li>
                <li><strong>性能指标</strong>：包含具体的性能数据和优化点</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2 id="business-logic-architecture">🧠 业务逻辑详细架构图</h2>

        <div class="legend">
            <h4>业务逻辑架构说明：</h4>
            <p>以下架构图展示了 delete_phrase 操作的核心业务逻辑实现，包含文本切割、差异分析、删除策略等具体算法：</p>
            <span class="legend-item legend-input">🔵 文本输入处理</span>
            <span class="legend-item legend-validation">🔴 文本分析算法</span>
            <span class="legend-item legend-process">🟣 业务策略执行</span>
            <span class="legend-item legend-database">🟠 数据库操作</span>
            <span class="legend-item legend-output">🟢 结果构建</span>
        </div>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🔵 Text Input Processing"
        A1["Input Data<br/>color: 0<br/>new_text: '苹果、香蕉、橙子、'<br/>old_text: '苹果、香蕉、苹果1、橙子、'"]
        A2["TextProcessor.split_text_by_comma()<br/>Algorithm:<br/>1. text.split('、')<br/>2. Handle tail_text logic<br/>3. Return TextSplitResult"]
        A3["Split Results<br/>old_blocks: ['苹果', '香蕉', '苹果1', '橙子']<br/>new_blocks: ['苹果', '香蕉', '橙子']<br/>tail_text: ''"]
    end

    subgraph "🔴 Text Analysis Algorithms"
        B1["TextProcessor.find_different_blocks()<br/>Algorithm:<br/>from collections import Counter<br/>source_count = Counter(old_blocks)<br/>target_count = Counter(new_blocks)<br/>differences = source - target"]
        B2["Difference Analysis<br/>deleted_blocks: ['苹果1']<br/>Logic: old_blocks中存在但new_blocks中不存在<br/>使用Counter统计差异"]
        B3["Pattern Recognition<br/>regex: r'^(.+?)(\\d+)$'<br/>match = re.search(pattern, '苹果1')<br/>word: '苹果', digit: '1'<br/>has_digit_suffix: True"]
    end

    subgraph "🟣 Business Strategy Execution"
        C1["Strategy Selection<br/>if has_digit_suffix:<br/>  _handle_delete_with_digit()<br/>else:<br/>  _handle_delete_without_digit()"]
        C2["Complex Delete Strategy<br/>1. Extract word and type from '苹果1'<br/>2. Delete target phrase (word='苹果', type=1)<br/>3. Find phrases to renumber (type > 1)<br/>4. Global renumbering process"]
        C3["Word Extraction<br/>_extract_word_from_block()<br/>Support formats:<br/>1. '你好_0' -> '你好' (underscore)<br/>2. '你好3' -> '你好' (digit suffix)<br/>3. '你好' -> '你好' (no suffix)"]
    end

    subgraph "🟠 Database Operations Detail"
        D1["Target Phrase Deletion<br/>SQL: DELETE FROM phrase<br/>WHERE word='苹果' AND type=1<br/>AND text_id=1234567890123456789<br/>Affected rows: 1"]
        D2["Renumbering Query<br/>SQL: SELECT * FROM phrase<br/>WHERE word='苹果' AND type > 1<br/>ORDER BY type ASC<br/>Results: [苹果2, 苹果3, ...]"]
        D3["Batch Update Process<br/>For each phrase in results:<br/>  old_type = phrase.type<br/>  new_type = old_type - 1<br/>  phrase.type = new_type<br/>  Update TextInfo text content"]
    end

    subgraph "🟢 Text Reconstruction"
        E1["Text Content Update<br/>For each affected TextInfo:<br/>old_word = word + str(old_type)<br/>new_word = word + str(new_type)<br/>text = text.replace(old_word, new_word)"]
        E2["Global Text Sync<br/>Update multiple TextInfo records:<br/>color=0: '苹果、香蕉、橙子、'<br/>color=1: '苹果1、葡萄、苹果2、'<br/>Maintain global numbering consistency"]
        E3["Response Construction<br/>updated_text_infos = [<br/>  {id: '123...', color: 0, text: '...'},<br/>  {id: '456...', color: 1, text: '...'}<br/>]"]
    end

    subgraph "🔧 Algorithm Details"
        F1["Counter Algorithm<br/>source_count = {'苹果': 2, '香蕉': 1, '橙子': 1}<br/>target_count = {'苹果': 1, '香蕉': 1, '橙子': 1}<br/>diff = source_count - target_count<br/>result = ['苹果'] (1 occurrence difference)"]
        F2["Regex Pattern Matching<br/>Pattern: r'^(.+?)(\\d+)$'<br/>Input: '苹果1'<br/>match.group(1): '苹果'<br/>match.group(2): '1'<br/>Supports Chinese characters + digits"]
        F3["Type Renumbering Logic<br/>Original: [苹果1, 苹果2, 苹果3]<br/>Delete: 苹果1<br/>Renumber: [苹果1, 苹果2] (shift down)<br/>Global consistency maintained"]
    end

    A1 --> A2
    A2 --> A3
    A3 --> B1
    B1 --> B2
    B2 --> B3
    B3 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> E1
    E1 --> E2
    E2 --> E3

    F1 -.-> B1
    F2 -.-> B3
    F3 -.-> D3

    classDef inputStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef analysisStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef outputStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef algorithmStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class A1,A2,A3 inputStyle
    class B1,B2,B3 analysisStyle
    class C1,C2,C3 businessStyle
    class D1,D2,D3 dataStyle
    class E1,E2,E3 outputStyle
    class F1,F2,F3 algorithmStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>🧠 核心算法特点：</h5>
            <ul>
                <li><strong>文本切割算法</strong>：基于顿号分隔符的智能文本块提取</li>
                <li><strong>差异检测算法</strong>：使用Python Counter进行高效的集合差异计算</li>
                <li><strong>模式识别算法</strong>：正则表达式识别中文词汇+数字后缀模式</li>
                <li><strong>全局重编号算法</strong>：跨TextInfo的一致性编号维护</li>
                <li><strong>文本重构算法</strong>：精确的字符串替换和文本同步</li>
            </ul>
        </div>

        <div class="highlight">
            <h5>💡 算法复杂度分析：</h5>
            <ul>
                <li><strong>文本切割</strong>：O(n) - 单次遍历字符串</li>
                <li><strong>差异检测</strong>：O(n) - Counter统计 + O(m) 差异计算</li>
                <li><strong>重编号查询</strong>：O(log n) - 数据库索引查询</li>
                <li><strong>批量更新</strong>：O(k) - k为需要更新的记录数</li>
                <li><strong>总体复杂度</strong>：O(n + m + k log n) - 线性时间复杂度</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2 id="comprehensive-architecture">🌟 综合融合架构图 v1.0</h2>

        <div class="legend">
            <h4>综合架构图说明：</h4>
            <p>以下架构图融合了数据流程、技术实现和业务逻辑三个维度，展示了完整的 delete_phrase 操作全貌：</p>
            <span class="legend-item legend-input">🔵 HTTP接口层 - 前端请求处理和路由匹配</span>
            <span class="legend-item legend-validation">🔴 数据验证层 - Pydantic验证和类型转换</span>
            <span class="legend-item legend-process">🟣 业务逻辑层 - 文本处理和算法执行</span>
            <span class="legend-item legend-database">🟠 数据持久层 - 数据库操作和事务管理</span>
            <span class="legend-item legend-output">🟢 响应构建层 - 结果处理和JSON响应</span>
        </div>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🌐 HTTP接口层"
        A1["🌍 前端请求<br/>DELETE /phrase/delete<br/>请求头: application/json<br/>请求体: {color:0, text:'苹果、香蕉、橙子、'}"]
        A2["⚡ FastAPI路由器<br/>路由装饰器: @router.delete('/delete')<br/>依赖注入: get_db() 获取数据库会话<br/>异常处理: BusinessException捕获"]
    end

    subgraph "🔴 数据验证解析层"
        B1["📋 Pydantic数据验证<br/>验证模型: TextInfoColorUpdate<br/>✅ color字段: 0 (范围0≤x≤8)<br/>✅ text字段: 长度12 (≤1000字符)<br/>✅ 必填字段存在性检查"]
        B2["🔧 JSON转Python对象<br/>验证通过的Python对象:<br/>TextInfoColorUpdate(<br/>  color=0,<br/>  text='苹果、香蕉、橙子、'<br/>)"]
    end

    subgraph "🟣 业务逻辑处理层"
        C1["🏭 词汇服务调用<br/>方法: PhraseService.delete_phrase()<br/>输入: color=0, new_text='苹果、香蕉、橙子、'<br/>数据库查询: TextInfo.filter(color==0).first()<br/>查询结果: old_text='苹果、香蕉、苹果1、橙子、'"]

        C2["✂️ 文本切割算法实现<br/>1. parts = text.split('、') 按顿号分割<br/>2. for i, part in enumerate(parts): 遍历每部分<br/>3. if i == len(parts)-1: 判断是否最后一部分<br/>4. if text.endswith('、'): 检查原文本结尾<br/>5. blocks.append(part) 或 tail_text = part<br/>结果: blocks=['苹果','香蕉','苹果1','橙子'], tail_text=''"]

        C3["🔍 差异检测算法实现<br/>1. source_count = Counter(old_blocks) 统计旧文本<br/>2. target_count = Counter(new_blocks) 统计新文本<br/>3. for block, source_num in source_count.items():<br/>4.   target_num = target_count.get(block, 0)<br/>5.   diff = source_num - target_num<br/>6.   differences.extend([block] * diff)<br/>结果: differences = ['苹果1']"]

        C4["🎯 模式识别算法实现<br/>1. pattern = r'^(.+?)(\\d+)$' 定义正则模式<br/>2. match = re.search(pattern, '苹果1') 执行匹配<br/>3. if match: 检查是否匹配成功<br/>4.   word = match.group(1) 提取词汇部分 '苹果'<br/>5.   type_value = int(match.group(2)) 提取数字 1<br/>6. has_digit_suffix = bool(match) 判断策略<br/>结果: 复杂删除 + 全局重新编号"]
    end

    subgraph "🟠 数据持久化层"
        D1["🗑️ 目标词汇删除<br/>SQL语句: DELETE FROM phrase<br/>删除条件: WHERE word='苹果' AND type=1<br/>文本ID: AND text_id=1234567890123456789<br/>影响行数: 1行被删除"]

        D2["🔄 重编号查询<br/>SQL语句: SELECT * FROM phrase<br/>查询条件: WHERE word='苹果' AND type > 1<br/>排序规则: ORDER BY type ASC<br/>查询结果: [苹果2@color1, 苹果3@color1]"]

        D3["📝 批量更新算法实现<br/>1. for phrase in phrase_list_to_update: 遍历待更新词汇<br/>2.   original_type = phrase.type 获取原编号<br/>3.   old_word = phrase.word + str(original_type) 构建旧词汇<br/>4.   new_type = original_type - 1 计算新编号<br/>5.   new_word = phrase.word + str(new_type) 构建新词汇<br/>6.   text_info.text = text.replace(old_word, new_word) 替换文本<br/>7.   phrase.type = new_type 更新编号<br/>结果: '苹果2'→'苹果1', '苹果3'→'苹果2'"]

        D4["💾 事务提交<br/>开始事务: BEGIN TRANSACTION<br/>  删除操作: DELETE 1条phrase记录<br/>  更新操作: UPDATE 2条phrase记录<br/>  更新操作: UPDATE 2条text_info记录<br/>提交事务: COMMIT (耗时约50ms)"]
    end

    subgraph "🟢 响应构建层"
        E1["🔧 文本重构<br/>全局文本同步:<br/>color=0: '苹果、香蕉、橙子、'<br/>color=1: '苹果1、葡萄、苹果2、'<br/>字符串替换: '苹果2'→'苹果1', '苹果3'→'苹果2'"]

        E2["📦 响应数据构建<br/>复杂删除响应格式:<br/>{<br/>  message: '删除成功',<br/>  updated_text_infos: [<br/>    {id:'123...', color:0, text:'...'},<br/>    {id:'456...', color:1, text:'...'}<br/>  ]<br/>}"]

        E3["🌐 HTTP响应<br/>JSON响应构建: JSONResponse(<br/>  content=response_data,<br/>  status_code=200,<br/>  headers={'Content-Type': 'application/json'}<br/>)"]
    end

    %% 主要数据流箭头及详细标注
    A1 -->|"HTTP请求体: 52字节<br/>解析JSON为字典对象"| A2
    A2 -->|"路由匹配: /phrase/delete<br/>注入数据库会话"| B1
    B1 -->|"验证通过<br/>类型转换完成"| B2
    B2 -->|"Pydantic对象创建完成<br/>准备进入业务逻辑"| C1
    C1 -->|"TextInfo查询完成<br/>old_text vs new_text对比"| C2
    C2 -->|"文本块提取完成<br/>准备进行差异比较"| C3
    C3 -->|"差异识别完成<br/>deleted_blocks = ['苹果1']"| C4
    C4 -->|"模式匹配成功<br/>选择复杂删除策略"| D1
    D1 -->|"目标词汇删除完成<br/>查找相关词汇"| D2
    D2 -->|"相关词汇查找完成<br/>开始重新编号"| D3
    D3 -->|"批量更新完成<br/>提交事务"| D4
    D4 -->|"数据库更新完成<br/>构建响应数据"| E1
    E1 -->|"文本同步完成<br/>构造响应对象"| E2
    E2 -->|"响应数据准备完成<br/>序列化为JSON"| E3

    %% 算法细节连接线
    C2 -.->|"核心逻辑: enumerate(parts)遍历<br/>endswith('、')判断结尾处理"| C3
    C3 -.->|"核心逻辑: Counter统计差异<br/>extend([block]*diff)扩展结果"| C4
    C4 -.->|"核心逻辑: match.group(1)提取词汇<br/>match.group(2)提取数字"| D1
    D2 -.->|"核心逻辑: ORDER BY type ASC<br/>确保重编号顺序正确"| D3
    D3 -.->|"核心逻辑: text.replace(old,new)<br/>phrase.type = new_type更新"| D4
    E1 -.->|"核心逻辑: 字符串精确替换<br/>跨TextInfo同步更新"| E2

    %% 错误处理路径
    B1 -.->|"验证错误<br/>ValidationError"| E3
    C1 -.->|"业务异常<br/>BusinessException"| E3
    D4 -.->|"数据库回滚<br/>错误时自动回滚"| E3

    classDef httpStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef validationStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef responseStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:3px

    class A1,A2 httpStyle
    class B1,B2 validationStyle
    class C1,C2,C3,C4 businessStyle
    class D1,D2,D3,D4 dataStyle
    class E1,E2,E3 responseStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>🌟 综合架构特点：</h5>
            <ul>
                <li><strong>多维度融合</strong>：数据流程 + 技术实现 + 业务算法的完整展示</li>
                <li><strong>详细标注</strong>：每个箭头都包含具体的数据变化和处理逻辑</li>
                <li><strong>算法可视化</strong>：核心算法的具体实现过程直接展示在图中</li>
                <li><strong>性能指标</strong>：包含时间复杂度、数据库操作耗时等关键指标</li>
                <li><strong>错误处理</strong>：虚线展示异常处理路径和回滚机制</li>
                <li><strong>数据追踪</strong>：从HTTP字节到数据库记录的完整数据变化链</li>
            </ul>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h4>🔄 数据流转路径</h4>
                <ul>
                    <li><strong>输入</strong>: HTTP JSON → Pydantic对象</li>
                    <li><strong>分析</strong>: 文本切割 → 差异检测 → 模式识别</li>
                    <li><strong>处理</strong>: 数据库删除 → 重编号 → 批量更新</li>
                    <li><strong>输出</strong>: 文本重构 → JSON响应 → HTTP返回</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🧠 核心算法集成</h4>
                <ul>
                    <li><strong>文本切割</strong>: split('、') + tail_text处理</li>
                    <li><strong>差异检测</strong>: Counter算法 + 集合运算</li>
                    <li><strong>模式匹配</strong>: 正则表达式 + 中文支持</li>
                    <li><strong>重编号</strong>: 全局一致性 + 批量更新</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>⚡ 性能优化点</h4>
                <ul>
                    <li><strong>算法复杂度</strong>: O(n + m + k log n)</li>
                    <li><strong>数据库优化</strong>: 索引查询 + 批量操作</li>
                    <li><strong>内存效率</strong>: 流式处理 + 对象复用</li>
                    <li><strong>网络优化</strong>: JSON压缩 + 响应缓存</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🛡️ 可靠性保障</h4>
                <ul>
                    <li><strong>数据验证</strong>: Pydantic类型安全</li>
                    <li><strong>事务管理</strong>: ACID特性保证</li>
                    <li><strong>异常处理</strong>: 多层错误捕获</li>
                    <li><strong>一致性</strong>: 全局编号同步</li>
                </ul>
            </div>
        </div>
    </div>

    </div>

    <div class="container">
        <h2 id="comprehensive-architecture-v2">🚀 综合融合架构图 v2.0</h2>

        <div class="legend">
            <h4>v2.0 架构图改进说明：</h4>
            <p>v2.0版本优化了层次分离，将算法实现移到工具层，会话管理移到配置层，业务层专注调用逻辑：</p>
            <span class="legend-item legend-input">🔵 HTTP接口层 - 路由匹配和依赖注入</span>
            <span class="legend-item legend-validation">🔴 数据验证层 - Pydantic验证和类型转换</span>
            <span class="legend-item legend-process">🟣 业务逻辑层 - 服务调用和流程控制</span>
            <span class="legend-item legend-database">🟠 数据持久层 - ORM操作和事务管理</span>
            <span class="legend-item legend-output">🟢 响应构建层 - 结果处理和JSON响应</span>
            <span class="legend-item" style="background-color: #fff9c4; color: #f57f17;">� 工具算法层 - 核心算法实现</span>
            <span class="legend-item" style="background-color: #e1f5fe; color: #0277bd;">🔧 配置管理层 - 数据库会话和Redis等配置</span>
        </div>

        <div class="node-section">
            <div class="node-header">🌐 节点1: HTTP请求处理</div>
            <div class="node-content">
                <h4>1.1 HTTP请求格式</h4>
                <pre><code>DELETE /phrase/delete HTTP/1.1
Host: localhost:8080
Content-Type: application/json
Content-Length: 52

{"color":0,"text":"苹果、香蕉、苹果1、橙子、"}</code></pre>

                <h4>1.2 FastAPI内部处理流程</h4>
                <pre><code class="language-python"># 1. 路由匹配
app = FastAPI()
route: APIRoute = app.router.route_class(
    path="/phrase/delete",
    endpoint=delete_phrase,
    methods=["DELETE"]
)

# 2. 请求解析
async def process_request(request: Request) -> Response:
    # 2.1 解析HTTP头
    content_type = request.headers.get("content-type")  # "application/json"
    content_length = request.headers.get("content-length")  # "52"

    # 2.2 读取请求体
    body_bytes: bytes = await request.body()
    # body_bytes = b'{"color":0,"text":"苹果、香蕉、苹果1、橙子、"}'

    # 2.3 JSON解析
    import json
    json_data: dict = json.loads(body_bytes.decode('utf-8'))
    # json_data = {"color": 0, "text": "苹果、香蕉、苹果1、橙子、"}

    return json_data</code></pre>

                <div class="data-flow">
                    <h5>数据变化：</h5>
                    <p><strong>HTTP原始数据:</strong> <code>b'{"color":0,"text":"苹果、香蕉、苹果1、橙子、"}'</code></p>
                    <p><strong>JSON解析后:</strong> <code>{"color": 0, "text": "苹果、香蕉、苹果1、橙子、"}</code></p>
                </div>
            </div>
        </div>

        <div class="node-section">
            <div class="node-header">🚦 节点2: FastAPI路由</div>
            <div class="node-content">
                <h4>2.1 路由定义和匹配</h4>
                <pre><code class="language-python">@router.delete("/delete")
async def delete_phrase(text_info: TextInfoColorUpdate, db: Session = Depends(get_db)):
    """删除词汇"""
    try:
        service = PhraseService(db)
        result = await service.delete_phrase(text_info.color, text_info.text)
        return result
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))</code></pre>

                <h4>2.2 依赖注入处理</h4>
                <pre><code class="language-python"># 数据库会话创建
def get_db() -> Session:
    """数据库会话依赖"""
    db = SessionLocal()  # 创建数据库会话
    try:
        yield db  # 生成器，提供会话
    finally:
        db.close()  # 确保会话关闭</code></pre>

                <div class="data-flow">
                    <h5>数据变化：</h5>
                    <p><strong>输入:</strong> JSON dict → Pydantic对象</p>
                    <p><strong>依赖注入:</strong> 数据库会话 <code>Session</code></p>
                    <p><strong>服务实例化:</strong> <code>PhraseService(db)</code></p>
                </div>
            </div>
        </div>

        <div class="node-section">
            <div class="node-header">🔧 节点3: Pydantic验证</div>
            <div class="node-content">
                <h4>3.1 Pydantic模型定义</h4>
                <pre><code class="language-python">class TextInfoColorUpdate(BaseModel):
    """词汇操作的请求模型（只需要color和text）"""
    color: int = Field(..., ge=0, le=8, description="颜色标识，0-8")
    text: str = Field(..., max_length=1000, description="文本内容")</code></pre>

                <h4>3.2 验证过程详解</h4>
                <pre><code class="language-python"># 步骤1: 字段存在性检查
required_fields = ["color", "text"]

# 步骤2: 类型转换和验证
# 2.1 color字段验证 - 类型检查
if not isinstance(color_value, int):
    color_value = int(color_value)  # 尝试类型转换

# 2.2 color范围验证
if not (0 <= color_value <= 8):
    raise ValidationError("color must be between 0 and 8")

# 2.3 text字段验证
if len(text_value) > 1000:
    raise ValidationError("text length must not exceed 1000 characters")</code></pre>

                <div class="data-flow">
                    <h5>验证规则检查结果：</h5>
                    <ul>
                        <li>✅ <strong>color_type_check:</strong> int</li>
                        <li>✅ <strong>color_range_check:</strong> 0 ≤ 0 ≤ 8</li>
                        <li>✅ <strong>text_type_check:</strong> str</li>
                        <li>✅ <strong>text_length_check:</strong> len=16 ≤ 1000</li>
                        <li>✅ <strong>required_fields_check:</strong> color, text present</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="node-section">
            <div class="node-header">🗄️ 节点4: 数据库ORM</div>
            <div class="node-content">
                <h4>4.1 SQLAlchemy查询构建</h4>
                <pre><code class="language-python"># 步骤1: 构建查询对象
query = db.query(TextInfo)

# 步骤2: 添加过滤条件
filtered_query = query.filter(TextInfo.color == color)

# 步骤3: 生成SQL语句
sql_statement = """
SELECT text_info.id, text_info.color, text_info.text
FROM text_info
WHERE text_info.color = 0
"""

# 步骤4: 执行查询
result: TextInfo = filtered_query.first()</code></pre>

                <h4>4.2 批量更新操作</h4>
                <pre><code class="language-python"># Phrase批量更新
for phrase in phrases:
    original_type = phrase.type
    phrase.type = original_type - 1  # 重新编号
    assert phrase in db.dirty  # SQLAlchemy自动跟踪

# TextInfo批量更新
text_info_mappings = [
    {"id": text_info.id, "text": text_info.text}
    for text_info in text_infos
]
db.bulk_update_mappings(TextInfo, text_info_mappings)

# 提交事务
db.commit()</code></pre>

                <div class="data-flow">
                    <h5>数据变化：</h5>
                    <p><strong>查询输入:</strong> color=0</p>
                    <p><strong>数据库返回:</strong> TextInfo(id=123..., color=0, text="苹果、香蕉、苹果1、苹果2、橙子、")</p>
                    <p><strong>批量更新:</strong> 2个Phrase对象，2个TextInfo对象</p>
                </div>
            </div>
        </div>

        <div class="node-section">
            <div class="node-header">🗃️ 节点5: 数据库</div>
            <div class="node-content">
                <h4>5.1 SQL执行详情</h4>
                <pre><code class="language-sql">-- 步骤1: 删除操作
DELETE FROM phrase
WHERE word = '苹果' AND type = 2 AND text_id = 1234567890123456789;
-- 影响行数: 1

-- 步骤2: 更新当前TextInfo
UPDATE text_info
SET text = '苹果、香蕉、苹果1、橙子、'
WHERE id = 1234567890123456789;
-- 影响行数: 1

-- 步骤3: 查询需要重新编号的Phrase
SELECT phrase.id, phrase.text_id, phrase.word, phrase.type
FROM phrase
WHERE phrase.word = '苹果' AND phrase.type > 2
ORDER BY phrase.type ASC;
-- 返回行数: 2

-- 步骤4: 批量更新Phrase
UPDATE phrase SET type = 2 WHERE id = 2345678901234567890;
UPDATE phrase SET type = 3 WHERE id = 3456789012345678901;
-- 影响行数: 2

-- 步骤5: 批量更新TextInfo
UPDATE text_info
SET text = '苹果2、葡萄、苹果3、'
WHERE id = 9876543210987654321;
-- 影响行数: 1

-- 步骤6: 事务提交
COMMIT;</code></pre>

                <div class="data-flow">
                    <h5>数据库操作统计：</h5>
                    <ul>
                        <li><strong>删除行数:</strong> 1 (phrase表)</li>
                        <li><strong>更新行数:</strong> 4 (text_info表2行，phrase表2行)</li>
                        <li><strong>查询返回:</strong> 2行</li>
                        <li><strong>事务时间:</strong> ~50ms</li>
                        <li><strong>影响表:</strong> text_info, phrase</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="node-section">
            <div class="node-header">📤 节点6: JSON响应序列化</div>
            <div class="node-content">
                <h4>6.1 响应数据构建</h4>
                <pre><code class="language-python"># 复杂删除响应（包含更新的文本信息）
if updated_text_infos:
    response_data = {
        "message": "删除成功",
        "updated_text_infos": [
            {
                "id": str(text_info.id),  # 转换为字符串避免JavaScript精度丢失
                "color": text_info.color,
                "text": text_info.text
            }
            for text_info in updated_text_infos
        ]
    }
# 简单删除响应（只有消息）
else:
    response_data = {"message": "删除成功"}</code></pre>

                <h4>6.2 JSON序列化过程</h4>
                <pre><code class="language-python"># 步骤1: Python对象转换为JSON字符串
json_string = json.dumps(response_data, ensure_ascii=False, separators=(',', ':'))

# 步骤2: 设置响应头
headers = {
    "Content-Type": "application/json; charset=utf-8",
    "Content-Length": str(len(json_string.encode('utf-8')))
}

# 步骤3: 创建HTTP响应
response = JSONResponse(
    content=response_data,
    status_code=200,
    headers=headers
)</code></pre>

                <div class="data-flow">
                    <h5>最终HTTP响应：</h5>
                    <pre><code>HTTP/1.1 200 OK
Content-Type: application/json; charset=utf-8
Content-Length: 168

{"message":"删除成功","updated_text_infos":[{"id":"1234567890123456789","color":0,"text":"苹果、香蕉、苹果1、橙子、"},{"id":"9876543210987654321","color":1,"text":"苹果2、葡萄、苹果3、"}]}</code></pre>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2 id="technical-summary">🎯 技术要点总结</h2>

        <div class="summary-grid">
            <div class="summary-card">
                <h4>🔧 关键技术栈</h4>
                <ul>
                    <li><strong>FastAPI:</strong> 现代Python Web框架，提供自动API文档和类型验证</li>
                    <li><strong>Pydantic:</strong> 数据验证和序列化，基于Python类型注解</li>
                    <li><strong>SQLAlchemy:</strong> Python ORM框架，提供对象关系映射和查询构建</li>
                    <li><strong>依赖注入:</strong> FastAPI的依赖系统，管理数据库会话生命周期</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>📊 数据流特点</h4>
                <ul>
                    <li><strong>类型安全:</strong> 从HTTP JSON到Python对象的完整类型验证链</li>
                    <li><strong>事务一致性:</strong> 复杂删除操作的多表更新在单一事务中完成</li>
                    <li><strong>全局编号:</strong> 跨TextInfo的词汇编号保持全局一致性</li>
                    <li><strong>智能响应:</strong> 根据操作复杂度返回不同格式的响应数据</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🎯 性能优化点</h4>
                <ul>
                    <li><strong>批量更新:</strong> 使用SQLAlchemy的bulk_update_mappings进行批量操作</li>
                    <li><strong>查询优化:</strong> 精确的WHERE条件和ORDER BY子句</li>
                    <li><strong>会话管理:</strong> 依赖注入确保数据库连接的正确生命周期</li>
                    <li><strong>JSON序列化:</strong> 大整数ID转换为字符串避免JavaScript精度丢失</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🔒 错误处理机制</h4>
                <ul>
                    <li><strong>Pydantic验证:</strong> 请求参数的类型和范围验证</li>
                    <li><strong>业务异常:</strong> 自定义BusinessException处理业务逻辑错误</li>
                    <li><strong>HTTP异常:</strong> FastAPI的HTTPException处理API层错误</li>
                    <li><strong>数据库事务:</strong> 自动回滚机制保证数据一致性</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2 id="conclusion">📋 总结</h2>

        <div class="highlight">
            <p>这个详细的数据流分析展示了 <code>delete_phrase</code> 操作从HTTP请求到数据库操作再到JSON响应的完整过程，每个节点都有明确的输入输出和数据转换逻辑。</p>
        </div>

        <h3>🎯 通过这种深入的技术分析，我们可以：</h3>
        <ol>
            <li><strong>理解系统架构:</strong> 掌握FastAPI + SQLAlchemy + Pydantic的完整技术栈</li>
            <li><strong>优化性能瓶颈:</strong> 识别数据库操作和序列化过程中的优化点</li>
            <li><strong>提高代码质量:</strong> 学习类型安全和错误处理的最佳实践</li>
            <li><strong>支持系统维护:</strong> 为后续的功能扩展和bug修复提供技术基础</li>
        </ol>

        <div class="data-flow">
            <h5>💡 应用价值：</h5>
            <p>这种详细的节点级分析方法可以应用到其他API操作的分析中，帮助团队建立统一的技术理解和开发标准。</p>
        </div>
    </div>

    <div class="container">
        <h2 id="node-analysis">� 节点详细分析</h2>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🌐 HTTP接口层"
        A1["🌍 前端请求<br/>DELETE /phrase/delete<br/>请求头: application/json<br/>请求体: {color:0, text:'苹果、香蕉、橙子、'}"]
        A2["⚡ FastAPI路由器<br/>路由装饰器: @router.delete('/delete')<br/>参数解析: text_info: TextInfoColorUpdate<br/>依赖注入: db: Session = Depends(get_db)"]
    end

    subgraph "🔧 配置管理层"
        CONFIG1["📊 数据库配置<br/>app.config.database.py:<br/>DATABASE_URL配置<br/>engine = create_engine(DATABASE_URL)<br/>SessionLocal = sessionmaker(engine)"]
        CONFIG2["� 会话管理<br/>get_db()函数实现:<br/>1. db = SessionLocal() 创建会话<br/>2. yield db 生成器返回会话<br/>3. finally: db.close() 确保关闭<br/>支持扩展: Redis会话、缓存配置等"]
    end

    subgraph "🔴 数据验证解析层"
        B1["📋 Pydantic数据验证<br/>验证模型: TextInfoColorUpdate<br/>✅ color字段: 0 (范围0≤x≤8)<br/>✅ text字段: 长度12 (≤1000字符)<br/>✅ 必填字段存在性检查"]
        B2["🔧 JSON转Python对象<br/>验证通过的Python对象:<br/>TextInfoColorUpdate(<br/>  color=0,<br/>  text='苹果、香蕉、橙子、'<br/>)"]
    end

    subgraph "🟣 业务逻辑层"
        C1["🏭 词汇服务初始化<br/>service = PhraseService(db)<br/>传入数据库会话进行初始化<br/>调用: service.delete_phrase(color, text)"]
        C2["📞 文本处理服务调用<br/>调用: TextProcessor.split_text_by_comma(old_text)<br/>调用: TextProcessor.split_text_by_comma(new_text)<br/>调用: TextProcessor.find_different_blocks(old_blocks, new_blocks)<br/>获取差异结果用于后续处理"]
        C3["🎯 删除策略选择<br/>遍历差异块: for deleted_block in differences:<br/>模式匹配: match = re.search(r'^(.+?)(\\d+)$', deleted_block)<br/>策略分支: if match: 复杂删除 else: 简单删除"]
    end

    subgraph "🟡 工具算法层"
        T1["✂️ 文本切割算法实现<br/>TextProcessor.split_text_by_comma():<br/>1. parts = text.split('、') 按顿号分割<br/>2. for i, part in enumerate(parts): 遍历每部分<br/>3. if i == len(parts)-1: 判断是否最后一部分<br/>4. if text.endswith('、'): 检查原文本结尾<br/>5. blocks.append(part) 或 tail_text = part<br/>返回: TextSplitResult(blocks, tail_text)"]

        T2["🔍 差异检测算法实现<br/>TextProcessor.find_different_blocks():<br/>1. source_count = Counter(old_blocks) 统计旧文本块<br/>2. target_count = Counter(new_blocks) 统计新文本块<br/>3. for block, source_num in source_count.items(): 遍历旧文本<br/>4.   target_num = target_count.get(block, 0) 获取新文本中的数量<br/>5.   diff = source_num - target_num 计算差异数量<br/>6.   differences.extend([block] * diff) 扩展差异列表<br/>返回: differences列表"]
    end

    subgraph "🟠 数据持久化层"
        D1["🗑️ 目标词汇删除<br/>SQL语句: DELETE FROM phrase<br/>删除条件: WHERE word='苹果' AND type=1<br/>文本ID: AND text_id=1234567890123456789<br/>影响行数: 1行被删除"]

        D2["🔄 重编号查询<br/>SQL语句: SELECT * FROM phrase<br/>查询条件: WHERE word='苹果' AND type > 1<br/>排序规则: ORDER BY type ASC<br/>查询结果: [苹果2@color1, 苹果3@color1]"]

        D3["📝 批量更新算法实现<br/>1. for phrase in phrase_list_to_update: 遍历待更新词汇<br/>2.   original_type = phrase.type 获取原编号<br/>3.   old_word = phrase.word + str(original_type) 构建旧词汇<br/>4.   new_type = original_type - 1 计算新编号<br/>5.   new_word = phrase.word + str(new_type) 构建新词汇<br/>6.   text_info.text = text.replace(old_word, new_word) 替换文本<br/>7.   phrase.type = new_type 更新编号"]

        D4["💾 事务提交<br/>开始事务: BEGIN TRANSACTION<br/>  删除操作: DELETE 1条phrase记录<br/>  更新操作: UPDATE 2条phrase记录<br/>  更新操作: UPDATE 2条text_info记录<br/>提交事务: COMMIT (耗时约50ms)"]
    end

    subgraph "🟢 响应构建层"
        E1["🔧 文本重构<br/>全局文本同步:<br/>color=0: '苹果、香蕉、橙子、'<br/>color=1: '苹果1、葡萄、苹果2、'<br/>字符串替换: '苹果2'→'苹果1', '苹果3'→'苹果2'"]

        E2["📦 响应数据构建<br/>复杂删除响应格式:<br/>{<br/>  message: '删除成功',<br/>  updated_text_infos: [<br/>    {id:'123...', color:0, text:'...'},<br/>    {id:'456...', color:1, text:'...'}<br/>  ]<br/>}"]

        E3["🌐 HTTP响应<br/>JSON响应构建: JSONResponse(<br/>  content=response_data,<br/>  status_code=200,<br/>  headers={'Content-Type': 'application/json'}<br/>)<br/>依赖注入自动关闭: db.close()"]
    end

    %% 主要数据流箭头及详细标注
    A1 -->|"HTTP请求体: 52字节<br/>解析JSON为字典对象"| A2
    A2 -->|"路由匹配成功<br/>触发依赖注入"| CONFIG2
    CONFIG2 -->|"数据库会话创建完成<br/>开始数据验证"| B1
    B1 -->|"验证通过<br/>类型转换完成"| B2
    B2 -->|"Pydantic对象创建完成<br/>进入业务逻辑层"| C1
    C1 -->|"服务初始化完成<br/>调用文本处理工具"| C2
    C2 -->|"工具调用完成<br/>获得差异结果"| C3
    C3 -->|"策略选择完成<br/>执行数据库操作"| D1
    D1 -->|"目标词汇删除完成<br/>查找相关词汇"| D2
    D2 -->|"相关词汇查找完成<br/>开始重新编号"| D3
    D3 -->|"批量更新完成<br/>提交事务"| D4
    D4 -->|"数据库更新完成<br/>构建响应数据"| E1
    E1 -->|"文本同步完成<br/>构造响应对象"| E2
    E2 -->|"响应数据准备完成<br/>序列化为JSON"| E3

    %% 工具层调用连接线
    C2 -.->|"调用工具算法<br/>传入文本参数"| T1
    C2 -.->|"调用工具算法<br/>传入文本块参数"| T2
    T1 -.->|"返回切割结果<br/>TextSplitResult对象"| C2
    T2 -.->|"返回差异列表<br/>differences数组"| C2

    %% 配置层连接
    CONFIG1 -->|"数据库引擎配置<br/>会话工厂创建"| CONFIG2

    %% 错误处理路径
    B1 -.->|"验证错误<br/>ValidationError"| E3
    C1 -.->|"业务异常<br/>BusinessException"| E3
    D4 -.->|"数据库回滚<br/>错误时自动回滚"| E3
    CONFIG2 -.->|"会话创建失败<br/>数据库连接异常"| E3

    classDef httpStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef configStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef validationStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef toolStyle fill:#fff9c4,stroke:#f57f17,stroke-width:3px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef responseStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:3px

    class A1,A2 httpStyle
    class CONFIG1,CONFIG2 configStyle
    class B1,B2 validationStyle
    class C1,C2,C3 businessStyle
    class T1,T2 toolStyle
    class D1,D2,D3,D4 dataStyle
    class E1,E2,E3 responseStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>🚀 v2.0 架构改进特点：</h5>
            <ul>
                <li><strong>🔧 配置层独立化</strong>：将数据库会话管理从HTTP层分离到独立配置层</li>
                <li><strong>🟡 工具层可视化</strong>：算法实现独立到工具层，业务层专注调用逻辑</li>
                <li><strong>📊 会话管理优化</strong>：支持数据库、Redis等多种会话配置的扩展</li>
                <li><strong>🔄 调用关系清晰</strong>：虚线连接展示业务层与工具层的调用关系</li>
                <li><strong>🛡️ 错误处理完善</strong>：配置层异常处理，支持连接失败等场景</li>
                <li><strong>📝 中文注释完整</strong>：差异检测算法的每个步骤都有详细中文说明</li>
                <li><strong>🏗️ 架构可扩展</strong>：配置层设计支持未来Redis、缓存等组件集成</li>
            </ul>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h4>🔧 配置管理层</h4>
                <ul>
                    <li><strong>数据库配置</strong>: DATABASE_URL、engine配置</li>
                    <li><strong>会话工厂</strong>: SessionLocal = sessionmaker(engine)</li>
                    <li><strong>依赖注入</strong>: get_db()生成器函数</li>
                    <li><strong>扩展支持</strong>: Redis、缓存等配置</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🟡 工具算法层</h4>
                <ul>
                    <li><strong>文本切割</strong>: split('、') + enumerate遍历</li>
                    <li><strong>差异检测</strong>: Counter统计 + extend扩展</li>
                    <li><strong>完整注释</strong>: 每个步骤都有中文说明</li>
                    <li><strong>独立测试</strong>: 算法逻辑与业务逻辑分离</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🌐 HTTP接口层</h4>
                <ul>
                    <li><strong>专注路由</strong>: 只负责请求路由和参数解析</li>
                    <li><strong>依赖注入</strong>: Depends(get_db)声明式注入</li>
                    <li><strong>职责单一</strong>: 不包含会话创建逻辑</li>
                    <li><strong>清晰边界</strong>: 与配置层职责分离</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>📊 架构优势</h4>
                <ul>
                    <li><strong>职责分离</strong>: 每层专注自己的核心功能</li>
                    <li><strong>易于扩展</strong>: 配置层支持多种数据源</li>
                    <li><strong>便于测试</strong>: 算法层可独立单元测试</li>
                    <li><strong>维护友好</strong>: 清晰的层次和调用关系</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2 id="comprehensive-architecture-v3">⚡ 综合融合架构图 v3.0</h2>

        <div class="legend">
            <h4>v3.0 架构图改进说明：</h4>
            <p>v3.0版本修正了数据流方向，体现真实的双向交互关系，HTTP接口层接入配置层会话后直接与各层交互：</p>
            <span class="legend-item legend-input">🔵 HTTP接口层 - 路由匹配和会话接入</span>
            <span class="legend-item" style="background-color: #e1f5fe; color: #0277bd;">🔧 配置管理层 - 数据库会话和Redis等配置</span>
            <span class="legend-item legend-validation">🔴 数据验证层 - Pydantic验证和类型转换</span>
            <span class="legend-item legend-process">🟣 业务逻辑层 - 服务调用和流程控制</span>
            <span class="legend-item" style="background-color: #fff9c4; color: #f57f17;">🟡 工具算法层 - 核心算法实现</span>
            <span class="legend-item legend-database">🟠 数据持久层 - ORM操作和事务管理</span>
            <span class="legend-item legend-output">🟢 响应构建层 - 结果处理和JSON响应</span>
        </div>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🌐 HTTP接口层"
        A1["🌍 前端请求<br/>DELETE /phrase/delete<br/>请求头: application/json<br/>请求体: {color:0, text:'苹果、香蕉、橙子、'}"]
        A2["⚡ FastAPI路由器<br/>路由装饰器: @router.delete('/delete')<br/>参数解析: text_info: TextInfoColorUpdate<br/>依赖注入: db: Session = Depends(get_db)"]
    end

    subgraph "🔧 配置管理层"
        CONFIG1["📊 数据库配置<br/>app.config.database.py:<br/>DATABASE_URL配置<br/>engine = create_engine(DATABASE_URL)<br/>SessionLocal = sessionmaker(engine)"]
        CONFIG2["🔄 会话管理<br/>get_db()函数实现:<br/>1. db = SessionLocal() 创建会话<br/>2. yield db 生成器返回会话<br/>3. finally: db.close() 确保关闭<br/>支持扩展: Redis会话、缓存配置等"]
    end

    subgraph "🔴 数据验证解析层"
        B1["📋 Pydantic数据验证<br/>验证模型: TextInfoColorUpdate<br/>✅ color字段: 0 (范围0≤x≤8)<br/>✅ text字段: 长度12 (≤1000字符)<br/>✅ 必填字段存在性检查"]
        B2["🔧 JSON转Python对象<br/>验证通过的Python对象:<br/>TextInfoColorUpdate(<br/>  color=0,<br/>  text='苹果、香蕉、橙子、'<br/>)"]
    end

    subgraph "🟣 业务逻辑层"
        C1["🏭 词汇服务初始化<br/>service = PhraseService(db)<br/>传入数据库会话进行初始化<br/>调用: service.delete_phrase(color, text)"]
        C2["📞 文本处理服务调用<br/>调用: TextProcessor.split_text_by_comma(old_text)<br/>调用: TextProcessor.split_text_by_comma(new_text)<br/>调用: TextProcessor.find_different_blocks(old_blocks, new_blocks)<br/>获取差异结果用于后续处理"]
        C3["🎯 删除策略选择<br/>遍历差异块: for deleted_block in differences:<br/>模式匹配: match = re.search(r'^(.+?)(\\d+)$', deleted_block)<br/>策略分支: if match: 复杂删除 else: 简单删除"]
    end

    subgraph "🟡 工具算法层"
        T1["✂️ 文本切割算法实现<br/>TextProcessor.split_text_by_comma():<br/>1. parts = text.split('、') 按顿号分割<br/>2. for i, part in enumerate(parts): 遍历每部分<br/>3. if i == len(parts)-1: 判断是否最后一部分<br/>4. if text.endswith('、'): 检查原文本结尾<br/>5. blocks.append(part) 或 tail_text = part<br/>返回: TextSplitResult(blocks, tail_text)"]

        T2["🔍 差异检测算法实现<br/>TextProcessor.find_different_blocks():<br/>1. source_count = Counter(old_blocks) 统计旧文本块<br/>2. target_count = Counter(new_blocks) 统计新文本块<br/>3. for block, source_num in source_count.items(): 遍历旧文本<br/>4.   target_num = target_count.get(block, 0) 获取新文本中的数量<br/>5.   diff = source_num - target_num 计算差异数量<br/>6.   differences.extend([block] * diff) 扩展差异列表<br/>返回: differences列表"]
    end

    subgraph "🟠 数据持久化层"
        D1["🗑️ 目标词汇删除<br/>SQL语句: DELETE FROM phrase<br/>删除条件: WHERE word='苹果' AND type=1<br/>文本ID: AND text_id=1234567890123456789<br/>影响行数: 1行被删除"]

        D2["🔄 重编号查询<br/>SQL语句: SELECT * FROM phrase<br/>查询条件: WHERE word='苹果' AND type > 1<br/>排序规则: ORDER BY type ASC<br/>查询结果: [苹果2@color1, 苹果3@color1]"]

        D3["📝 批量更新算法实现<br/>1. for phrase in phrase_list_to_update: 遍历待更新词汇<br/>2.   original_type = phrase.type 获取原编号<br/>3.   old_word = phrase.word + str(original_type) 构建旧词汇<br/>4.   new_type = original_type - 1 计算新编号<br/>5.   new_word = phrase.word + str(new_type) 构建新词汇<br/>6.   text_info.text = text.replace(old_word, new_word) 替换文本<br/>7.   phrase.type = new_type 更新编号"]

        D4["💾 事务提交<br/>开始事务: BEGIN TRANSACTION<br/>  删除操作: DELETE 1条phrase记录<br/>  更新操作: UPDATE 2条phrase记录<br/>  更新操作: UPDATE 2条text_info记录<br/>提交事务: COMMIT (耗时约50ms)"]
    end

    subgraph "🟢 响应构建层"
        E1["🔧 文本重构<br/>全局文本同步:<br/>color=0: '苹果、香蕉、橙子、'<br/>color=1: '苹果1、葡萄、苹果2、'<br/>字符串替换: '苹果2'→'苹果1', '苹果3'→'苹果2'"]

        E2["📦 响应数据构建<br/>复杂删除响应格式:<br/>{<br/>  message: '删除成功',<br/>  updated_text_infos: [<br/>    {id:'123...', color:0, text:'...'},<br/>    {id:'456...', color:1, text:'...'}<br/>  ]<br/>}"]

        E3["🌐 HTTP响应<br/>JSON响应构建: JSONResponse(<br/>  content=response_data,<br/>  status_code=200,<br/>  headers={'Content-Type': 'application/json'}<br/>)<br/>依赖注入自动关闭: db.close()"]
    end

    %% v3.0 修正的数据流方向 - 体现真实的双向交互
    A1 <-->|"HTTP请求/响应<br/>52字节JSON数据"| A2

    %% HTTP接口层接入配置层会话
    A2 <-->|"依赖注入请求/会话返回<br/>Depends(get_db)"| CONFIG2
    CONFIG1 -->|"配置加载<br/>引擎和会话工厂"| CONFIG2

    %% HTTP接口层直接与验证层交互
    A2 <-->|"数据传递/验证结果<br/>TextInfoColorUpdate对象"| B1
    B1 <-->|"验证处理/类型转换<br/>Pydantic对象"| B2

    %% HTTP接口层直接与业务层交互
    A2 <-->|"业务调用/处理结果<br/>service.delete_phrase()"| C1
    C1 <-->|"服务初始化/执行结果<br/>数据库会话传递"| C2
    C2 <-->|"策略选择/执行结果<br/>差异分析结果"| C3

    %% 业务层与工具层的双向调用
    C2 <-->|"算法调用/结果返回<br/>文本处理请求"| T1
    C2 <-->|"算法调用/结果返回<br/>差异检测请求"| T2

    %% 业务层与数据层的双向交互
    C3 <-->|"数据操作请求/执行结果<br/>删除和更新操作"| D1
    D1 <-->|"查询请求/数据返回<br/>相关词汇查询"| D2
    D2 <-->|"更新请求/执行结果<br/>批量重编号"| D3
    D3 <-->|"事务请求/提交结果<br/>数据一致性保证"| D4

    %% 数据层与响应层的双向交互
    D4 <-->|"数据结果/响应构建<br/>更新后的数据"| E1
    E1 <-->|"文本同步/构建结果<br/>全局一致性"| E2
    E2 <-->|"响应构建/JSON序列化<br/>最终响应数据"| E3

    %% HTTP接口层直接接收最终响应
    A2 <-->|"响应接收/返回客户端<br/>JSONResponse对象"| E3

    %% 错误处理的双向路径
    B1 -.->|"验证错误<br/>ValidationError"| E3
    C1 -.->|"业务异常<br/>BusinessException"| E3
    D4 -.->|"数据库异常<br/>回滚处理"| E3
    CONFIG2 -.->|"会话异常<br/>连接失败"| E3

    classDef httpStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef configStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef validationStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef toolStyle fill:#fff9c4,stroke:#f57f17,stroke-width:3px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef responseStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:3px

    class A1,A2 httpStyle
    class CONFIG1,CONFIG2 configStyle
    class B1,B2 validationStyle
    class C1,C2,C3 businessStyle
    class T1,T2 toolStyle
    class D1,D2,D3,D4 dataStyle
    class E1,E2,E3 responseStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>⚡ v3.0 架构改进特点：</h5>
            <ul>
                <li><strong>🔄 真实数据流</strong>：修正箭头方向，体现实际的双向交互关系</li>
                <li><strong>🌐 HTTP层中心化</strong>：接口层接入配置层会话后，直接与各层交互</li>
                <li><strong>↔️ 双向交互</strong>：不再强制单向流，体现请求/响应的真实模式</li>
                <li><strong>🔧 配置层独立</strong>：会话管理独立，支持多种数据源配置</li>
                <li><strong>🎯 控制流清晰</strong>：HTTP层作为控制中心，协调各层协作</li>
                <li><strong>🛡️ 异常处理</strong>：各层异常都能正确传递到响应层</li>
                <li><strong>📊 架构现实</strong>：更贴近实际开发中的调用关系</li>
            </ul>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h4>🔄 v3.0 核心改进</h4>
                <ul>
                    <li><strong>数据流修正</strong>: 双向箭头体现真实交互</li>
                    <li><strong>控制中心</strong>: HTTP层协调各层协作</li>
                    <li><strong>会话接入</strong>: 配置层提供会话，接口层使用</li>
                    <li><strong>异常传递</strong>: 各层异常正确路由到响应层</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🌐 HTTP接口层职责</h4>
                <ul>
                    <li><strong>路由匹配</strong>: FastAPI装饰器路由</li>
                    <li><strong>会话接入</strong>: 从配置层获取数据库会话</li>
                    <li><strong>层间协调</strong>: 直接与验证层、业务层交互</li>
                    <li><strong>响应返回</strong>: 接收最终响应并返回客户端</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>↔️ 双向交互模式</h4>
                <ul>
                    <li><strong>请求/响应</strong>: 每个调用都有返回值</li>
                    <li><strong>数据传递</strong>: 参数传入，结果返回</li>
                    <li><strong>异常处理</strong>: 异常向上传播</li>
                    <li><strong>资源管理</strong>: 会话创建和关闭</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🎯 架构优势</h4>
                <ul>
                    <li><strong>更真实</strong>: 反映实际开发中的调用关系</li>
                    <li><strong>更清晰</strong>: HTTP层作为明确的控制中心</li>
                    <li><strong>更灵活</strong>: 支持复杂的交互模式</li>
                    <li><strong>更实用</strong>: 便于理解和实现</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="container">
        <h2 id="comprehensive-architecture-v4">🎯 综合融合架构图 v4.0</h2>

        <div class="legend">
            <h4>v4.0 架构图改进说明：</h4>
            <p>v4.0版本在v3.0基础上优化了业务逻辑层和数据持久化层的职责分离，明确区分业务处理和数据操作：</p>
            <span class="legend-item legend-input">🔵 HTTP接口层 - 路由匹配和会话接入</span>
            <span class="legend-item" style="background-color: #e1f5fe; color: #0277bd;">🔧 配置管理层 - 数据库会话和Redis等配置</span>
            <span class="legend-item legend-validation">🔴 数据验证层 - Pydantic验证和类型转换</span>
            <span class="legend-item legend-process">🟣 业务逻辑层 - 业务规则和数据处理逻辑</span>
            <span class="legend-item" style="background-color: #fff9c4; color: #f57f17;">🟡 工具算法层 - 核心算法实现</span>
            <span class="legend-item" style="background-color: #f3e5f5; color: #7b1fa2;">📊 模型层 - SQLAlchemy ORM和Pydantic Schema</span>
            <span class="legend-item legend-database">🟠 数据持久层 - 纯粹的数据库操作</span>
            <span class="legend-item legend-output">🟢 响应构建层 - 结果处理和JSON响应</span>
        </div>

        <div class="mermaid-container">
            <div class="mermaid">
graph TB
    subgraph "🌐 HTTP接口层"
        A1["🌍 前端请求<br/>DELETE /phrase/delete<br/>请求头: application/json<br/>请求体: {color:0, text:'苹果、香蕉、橙子、'}"]
        A2["⚡ FastAPI路由器<br/>路由装饰器: @router.delete('/delete')<br/>参数解析: text_info: TextInfoColorUpdate<br/>依赖注入: db: Session = Depends(get_db)"]
    end

    subgraph "🔧 配置管理层"
        CONFIG1["📊 数据库配置<br/>app.config.database.py:<br/>DATABASE_URL配置<br/>engine = create_engine(DATABASE_URL)<br/>SessionLocal = sessionmaker(engine)"]
        CONFIG2["🔄 会话管理<br/>get_db()函数实现:<br/>1. db = SessionLocal() 创建会话<br/>2. yield db 生成器返回会话<br/>3. finally: db.close() 确保关闭<br/>支持扩展: Redis会话、缓存配置等"]
    end

    subgraph "🔴 数据验证解析层"
        B1["📋 Pydantic数据验证<br/>验证模型: TextInfoColorUpdate<br/>✅ color字段: 0 (范围0≤x≤8)<br/>✅ text字段: 长度12 (≤1000字符)<br/>✅ 必填字段存在性检查"]
        B2["🔧 JSON转Python对象<br/>验证通过的Python对象:<br/>TextInfoColorUpdate(<br/>  color=0,<br/>  text='苹果、香蕉、橙子、'<br/>)"]
    end

    subgraph "🟣 业务逻辑层"
        C1["🏭 词汇服务初始化<br/>service = PhraseService(db)<br/>传入数据库会话进行初始化<br/>调用: service.delete_phrase(color, text)"]
        C2["📞 文本处理服务调用<br/>调用: TextProcessor.split_text_by_comma(old_text)<br/>调用: TextProcessor.split_text_by_comma(new_text)<br/>调用: TextProcessor.find_different_blocks(old_blocks, new_blocks)<br/>获取差异结果用于后续处理"]
        C3["🎯 删除策略选择<br/>遍历差异块: for deleted_block in differences:<br/>模式匹配: match = re.search(r'^(.+?)(\\d+)$', deleted_block)<br/>策略分支: if match: 复杂删除 else: 简单删除"]
        C4["🔄 重编号业务逻辑<br/>1. phrase_list_to_update = 查询需要重编号的词汇列表<br/>2. for phrase in phrase_list_to_update: 遍历每个需要重编号的词汇<br/>3.   original_type = phrase.type 获取原编号<br/>4.   new_type = original_type - 1 计算新编号<br/>5.   old_word = phrase.word + str(original_type) 构建旧词汇<br/>6.   new_word = phrase.word + str(new_type) 构建新词汇<br/>7. 构建更新映射: {phrase_id: new_type, text_id: new_text}"]
        C5["📝 全局文本同步逻辑<br/>1. affected_text_infos = 收集所有受影响的TextInfo记录<br/>2. for text_info in affected_text_infos: 遍历每个受影响的文本记录<br/>3.   updated_text = text_info.text 获取当前文本<br/>4.   for old_word, new_word in replacement_map: 应用字符串替换规则<br/>5.     updated_text = updated_text.replace(old_word, new_word) 执行文本替换<br/>6.   text_info.text = updated_text 更新文本内容<br/>7. 验证全局编号一致性并构建批量更新请求"]
    end

    subgraph "🟡 工具算法层"
        T1["✂️ 文本切割算法实现<br/>TextProcessor.split_text_by_comma():<br/>1. parts = text.split('、') 按顿号分割文本<br/>2. for i, part in enumerate(parts): 遍历每个分割部分<br/>3. if i == len(parts)-1: 判断是否为最后一个部分<br/>4.   if text.endswith('、'): 检查原文本是否以顿号结尾<br/>5.     if part: blocks.append(part) 非空则添加到块列表<br/>6.     tail_text = '' 尾部文本为空<br/>7.   else: tail_text = part 最后部分作为尾部文本<br/>8. else: if part: blocks.append(part) 中间部分添加到块列表<br/>返回: TextSplitResult(blocks, tail_text)"]

        T2["🔍 差异检测算法实现<br/>TextProcessor.find_different_blocks():<br/>1. source_count = Counter(old_blocks) 统计旧文本块出现次数<br/>2. target_count = Counter(new_blocks) 统计新文本块出现次数<br/>3. for block, source_num in source_count.items(): 遍历旧文本中的每个块<br/>4.   target_num = target_count.get(block, 0) 获取该块在新文本中的出现次数<br/>5.   diff = source_num - target_num 计算差异数量（旧-新）<br/>6.   if diff > 0: 如果旧文本中该块更多<br/>7.     differences.extend([block] * diff) 将差异块添加到结果列表<br/>返回: differences差异块列表"]
    end

    subgraph "� 模型层"
        M1["🗄️ SQLAlchemy ORM模型<br/>TextInfo(Base):<br/>  id: BigInteger(PK)<br/>  color: Integer(unique=True)<br/>  text: String<br/>  phrases: relationship('Phrase')<br/>Phrase(Base):<br/>  id: BigInteger(PK)<br/>  text_id: BigInteger(FK)<br/>  word: String(255)<br/>  type: Integer<br/>  text_info: relationship('TextInfo')"]

        M2["📋 Pydantic Schema模型<br/>TextInfoColorUpdate:<br/>  color: int = Field(ge=0, le=8)<br/>  text: str = Field(max_length=1000)<br/>PhraseResponse:<br/>  id: int<br/>  text_id: int<br/>  word: str<br/>  phrase_type: int<br/>  @field_serializer('id', 'text_id')<br/>  def serialize_ids(value) -> str"]
    end

    subgraph "�🟠 数据持久化层"
        D1["🗑️ 目标词汇删除<br/>SQL语句: DELETE FROM phrase<br/>删除条件: WHERE word='苹果' AND type=1<br/>文本ID: AND text_id=1234567890123456789<br/>执行操作: db.execute(delete_stmt, params)<br/>影响行数: affected_rows = cursor.rowcount<br/>返回结果: {'deleted_count': 1}"]

        D2["� 重编号查询<br/>SQL语句: SELECT id, word, type, text_id FROM phrase<br/>查询条件: WHERE word='苹果' AND type > 1<br/>排序规则: ORDER BY type ASC<br/>执行查询: result = db.execute(select_stmt, params)<br/>查询结果: [Phrase(id=2, word='苹果', type=2, text_id=456), ...]<br/>返回数据: 需要重编号的词汇列表"]

        D3["� 批量更新操作<br/>1. 批量更新Phrase表编号:<br/>   SQL: UPDATE phrase SET type=? WHERE id=?<br/>   执行: executemany([(1, 2), (2, 3), ...])<br/>2. 批量更新TextInfo表文本:<br/>   SQL: UPDATE text_info SET text=? WHERE id=?<br/>   执行: executemany([('新文本1', 123), ('新文本2', 456)])<br/>返回统计: {'phrase_updated': 2, 'text_updated': 2}"]

        D4["💾 事务管理<br/>try:<br/>  db.begin() # 开始事务<br/>  delete_result = 执行D1删除操作<br/>  query_result = 执行D2查询操作<br/>  update_result = 执行D3批量更新<br/>  db.commit() # 提交事务<br/>except Exception as e:<br/>  db.rollback() # 回滚事务<br/>  raise e"]
    end

    subgraph "🟢 响应构建层"
        E1["🔧 响应数据整理<br/>1. 收集业务层处理结果<br/>2. 格式化更新后的文本信息<br/>3. 构建标准响应结构<br/>4. 添加操作状态信息"]

        E2["📦 响应数据构建<br/>复杂删除响应格式:<br/>{<br/>  message: '删除成功',<br/>  updated_text_infos: [<br/>    {id:'123...', color:0, text:'...'},<br/>    {id:'456...', color:1, text:'...'}<br/>  ]<br/>}"]

        E3["🌐 HTTP响应<br/>JSON响应构建: JSONResponse(<br/>  content=response_data,<br/>  status_code=200,<br/>  headers={'Content-Type': 'application/json'}<br/>)<br/>依赖注入自动关闭: db.close()"]
    end

    %% v4.0 优化的数据流 - 明确业务逻辑和数据操作的分离
    A1 <-->|"HTTP请求/响应<br/>52字节JSON数据"| A2

    %% HTTP接口层接入配置层会话
    A2 <-->|"依赖注入请求/会话返回<br/>Depends(get_db)"| CONFIG2
    CONFIG1 -->|"配置加载<br/>引擎和会话工厂"| CONFIG2

    %% HTTP接口层直接与验证层交互
    A2 <-->|"数据传递/验证结果<br/>TextInfoColorUpdate对象"| B1
    B1 <-->|"验证处理/类型转换<br/>Pydantic对象"| B2

    %% HTTP接口层直接与业务层交互
    A2 <-->|"业务调用/处理结果<br/>service.delete_phrase()"| C1
    C1 <-->|"服务初始化/执行结果<br/>数据库会话传递"| C2
    C2 <-->|"策略选择/执行结果<br/>差异分析结果"| C3

    %% 业务层内部的逻辑流程
    C3 <-->|"重编号需求/处理结果<br/>编号分配策略"| C4
    C4 <-->|"文本同步需求/处理结果<br/>全局一致性保证"| C5

    %% 业务层与工具层的双向调用
    C2 <-->|"算法调用/结果返回<br/>文本处理请求"| T1
    C2 <-->|"算法调用/结果返回<br/>差异检测请求"| T2

    %% 验证层与模型层的交互
    B1 <-->|"模型验证/验证结果<br/>Pydantic Schema"| M2
    B2 <-->|"对象创建/验证对象<br/>TextInfoColorUpdate"| M2

    %% 业务层与模型层的交互
    C1 <-->|"模型操作/数据对象<br/>ORM实例管理"| M1
    C4 <-->|"模型查询/对象列表<br/>Phrase对象操作"| M1
    C5 <-->|"模型更新/更新结果<br/>TextInfo对象操作"| M1

    %% 模型层与数据层的交互
    M1 <-->|"ORM映射/SQL执行<br/>对象关系映射"| D1
    M1 <-->|"ORM查询/结果映射<br/>查询对象转换"| D2
    M1 <-->|"ORM更新/批量操作<br/>对象状态同步"| D3

    %% 业务层与数据层的双向交互 - 明确分离
    C3 <-->|"删除请求/执行结果<br/>具体删除参数"| D1
    C4 <-->|"查询请求/数据返回<br/>查询条件和结果"| D2
    C5 <-->|"更新请求/执行结果<br/>批量更新数据"| D3
    C1 <-->|"事务控制/执行结果<br/>事务边界管理"| D4

    %% 数据层与响应层的双向交互
    D4 <-->|"数据结果/响应构建<br/>最终数据状态"| E1
    E1 <-->|"数据整理/构建结果<br/>格式化数据"| E2
    E2 <-->|"响应构建/JSON序列化<br/>最终响应数据"| E3

    %% HTTP接口层直接接收最终响应
    A2 <-->|"响应接收/返回客户端<br/>JSONResponse对象"| E3

    %% 错误处理的双向路径
    B1 -.->|"验证错误<br/>ValidationError"| E3
    C1 -.->|"业务异常<br/>BusinessException"| E3
    D4 -.->|"数据库异常<br/>回滚处理"| E3
    CONFIG2 -.->|"会话异常<br/>连接失败"| E3

    classDef httpStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px
    classDef configStyle fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef validationStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px
    classDef businessStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef toolStyle fill:#fff9c4,stroke:#f57f17,stroke-width:3px
    classDef modelStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px
    classDef dataStyle fill:#fff3e0,stroke:#f57c00,stroke-width:3px
    classDef responseStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:3px

    class A1,A2 httpStyle
    class CONFIG1,CONFIG2 configStyle
    class B1,B2 validationStyle
    class C1,C2,C3,C4,C5 businessStyle
    class T1,T2 toolStyle
    class M1,M2 modelStyle
    class D1,D2,D3,D4 dataStyle
    class E1,E2,E3 responseStyle
            </div>
        </div>

        <div class="data-flow">
            <h5>🎯 v4.0 架构改进特点：</h5>
            <ul>
                <li><strong>🎯 职责明确分离</strong>：业务逻辑层专注业务规则，数据持久层专注数据操作</li>
                <li><strong>🟣 业务层细化</strong>：新增C4重编号业务逻辑和C5全局文本同步逻辑</li>
                <li><strong>� 模型层补充</strong>：新增模型层，包含SQLAlchemy ORM模型和Pydantic Schema模型</li>
                <li><strong>�🟠 数据层纯化</strong>：数据层只负责纯粹的CRUD操作，不包含业务逻辑</li>
                <li><strong>🔄 流程优化</strong>：业务层内部流程更清晰，数据操作更规范</li>
                <li><strong>📊 架构完整</strong>：8层架构覆盖从HTTP接口到数据库的完整链路</li>
                <li><strong>🛡️ 异常处理</strong>：保持原有的异常处理机制</li>
                <li><strong>↔️ 双向交互</strong>：继承v3.0的双向交互优势</li>
            </ul>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h4>🎯 v4.0 核心优化</h4>
                <ul>
                    <li><strong>职责分离</strong>: 业务逻辑与数据操作明确分离</li>
                    <li><strong>业务细化</strong>: 重编号和文本同步逻辑独立</li>
                    <li><strong>数据纯化</strong>: 数据层只负责纯粹的数据库操作</li>
                    <li><strong>流程清晰</strong>: 业务处理流程更加规范</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🟣 业务逻辑层优化</h4>
                <ul>
                    <li><strong>C4 重编号逻辑</strong>: 编号分配策略和映射关系</li>
                    <li><strong>C5 文本同步</strong>: 全局文本一致性保证</li>
                    <li><strong>业务协调</strong>: 统筹数据层操作执行</li>
                    <li><strong>规则处理</strong>: 专注业务规则实现</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>🟠 数据持久层优化</h4>
                <ul>
                    <li><strong>D1 单条删除</strong>: 纯粹的DELETE操作</li>
                    <li><strong>D2 条件查询</strong>: 纯粹的SELECT操作</li>
                    <li><strong>D3 批量更新</strong>: 纯粹的UPDATE操作</li>
                    <li><strong>D4 事务管理</strong>: 纯粹的事务控制</li>
                </ul>
            </div>

            <div class="summary-card">
                <h4>📊 架构优势</h4>
                <ul>
                    <li><strong>单一职责</strong>: 每层职责明确，便于维护</li>
                    <li><strong>易于测试</strong>: 业务逻辑和数据操作可独立测试</li>
                    <li><strong>便于扩展</strong>: 新增业务规则不影响数据层</li>
                    <li><strong>代码复用</strong>: 数据操作可被多个业务场景复用</li>
                </ul>
            </div>
        </div>

        <div class="data-flow">
            <h5>🔄 v3.0 vs v4.0 对比：</h5>
            <div class="summary-grid">
                <div class="summary-card">
                    <h4>🟣 业务逻辑层变化</h4>
                    <ul>
                        <li><strong>v3.0</strong>: 包含部分数据操作逻辑</li>
                        <li><strong>v4.0</strong>: 专注纯粹的业务规则处理</li>
                        <li><strong>新增</strong>: C4重编号逻辑、C5文本同步逻辑</li>
                        <li><strong>优化</strong>: 业务流程更加清晰规范</li>
                    </ul>
                </div>

                <div class="summary-card">
                    <h4>🟠 数据持久层变化</h4>
                    <ul>
                        <li><strong>v3.0</strong>: 包含业务逻辑处理</li>
                        <li><strong>v4.0</strong>: 只负责纯粹的数据库操作</li>
                        <li><strong>移除</strong>: 编号计算、文本替换等业务逻辑</li>
                        <li><strong>专注</strong>: CRUD操作和事务管理</li>
                    </ul>
                </div>

                <div class="summary-card">
                    <h4>🔄 数据流优化</h4>
                    <ul>
                        <li><strong>业务内部流程</strong>: C3→C4→C5 业务链条</li>
                        <li><strong>数据操作分离</strong>: 业务层→数据层单向请求</li>
                        <li><strong>职责边界</strong>: 业务决策与数据执行分离</li>
                        <li><strong>维护性提升</strong>: 修改业务规则不影响数据层</li>
                    </ul>
                </div>

                <div class="summary-card">
                    <h4>🎯 实际应用价值</h4>
                    <ul>
                        <li><strong>开发效率</strong>: 职责清晰，开发更高效</li>
                        <li><strong>测试友好</strong>: 业务逻辑和数据操作可独立测试</li>
                        <li><strong>代码复用</strong>: 数据层可被多个业务场景复用</li>
                        <li><strong>架构演进</strong>: 便于后续架构升级和重构</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>

    <!-- v5.0 信息分层架构 -->
    <div class="version-section">
        <h2 id="v5-0">v5.0 信息分层架构 - 认知负荷优化版本</h2>

        <div class="legend">
            <h4>v5.0 信息分层设计理念：</h4>
            <p>v5.0版本采用认知科学原理，按照信息抽象层次和认知负荷进行重新组织，支持渐进式信息展示：</p>
            <div class="info-layers">
                <span class="layer-item" style="background-color: #e8f5e8; color: #2e7d32;">🎯 业务概念层 - 纯中文业务描述，用户视角</span>
                <span class="layer-item" style="background-color: #e3f2fd; color: #1976d2;">⚙️ 技术架构层 - 系统组件和设计模式</span>
                <span class="layer-item" style="background-color: #fff3e0; color: #f57c00;">💻 实现细节层 - 代码、SQL和算法</span>
                <span class="layer-item" style="background-color: #fce4ec; color: #c2185b;">📊 执行状态层 - 运行时数据和结果</span>
            </div>
        </div>

        <style>
            .info-layers {
                display: flex;
                flex-direction: column;
                gap: 8px;
                margin-top: 10px;
            }
            .layer-item {
                padding: 8px 12px;
                border-radius: 6px;
                font-weight: 500;
                border: 1px solid rgba(0,0,0,0.1);
            }
            .layer-toggle {
                background: #f5f5f5;
                border: 1px solid #ddd;
                padding: 8px 12px;
                margin: 5px 0;
                cursor: pointer;
                border-radius: 4px;
                font-weight: bold;
                transition: background-color 0.3s;
            }
            .layer-toggle:hover {
                background: #e0e0e0;
            }
            .layer-toggle.active {
                background: #2196f3;
                color: white;
            }
            .layer-content {
                display: none;
                padding: 15px;
                border: 1px solid #ddd;
                border-top: none;
                background: #fafafa;
            }
            .layer-content.active {
                display: block;
            }
            .business-flow {
                background: #e8f5e8;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #4caf50;
            }
            .tech-component {
                background: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #2196f3;
            }
            .code-detail {
                background: #fff3e0;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #ff9800;
                font-family: 'Courier New', monospace;
            }
            .runtime-data {
                background: #fce4ec;
                padding: 15px;
                border-radius: 8px;
                margin: 10px 0;
                border-left: 4px solid #e91e63;
            }
        </style>

        <div class="mermaid-container">
            <h3>v5.0 分层信息架构图</h3>

            <div class="mermaid">
graph LR
    %% 左侧：业务概念层 + 技术架构层（垂直排列）
    subgraph "🎯 业务概念层"
        B1["📋 删除词汇业务流程<br/>从文本中删除指定词汇<br/>保持全局编号连续性<br/>支持事务回滚保证完整性"]
        B2["🔄 业务处理步骤<br/>文本差异识别 → 删除策略判断<br/>编号重新分配 → 全局文本同步<br/>结果验证确认"]
        B3["🏭 词汇服务初始化<br/>创建词汇管理服务实例<br/>封装业务逻辑提供统一接口"]
        B4["📞 文本处理服务调用<br/>文本切割和差异检测<br/>为删除策略提供数据基础"]
        B5["🎯 删除策略选择<br/>正则匹配判断词汇特征<br/>选择最适合的处理方式"]
        B6["🔄 重编号业务逻辑<br/>计算新编号构建映射关系<br/>确保全局编号连续性"]
        B7["📝 全局文本同步<br/>更新所有相关文本记录<br/>保证全局数据一致性"]
    end

    subgraph "⚙️ 技术架构层"
        T1["� HTTP接口层架构<br/>FastAPI路由+依赖注入<br/>自动API文档生成"]
        T2["🔧 配置管理层架构<br/>数据库配置+会话管理<br/>支持Redis等扩展组件"]
        T3["🔴 数据验证层架构<br/>Pydantic验证模型<br/>类型安全的数据转换"]
        T4["🟣 业务逻辑层架构<br/>PhraseService服务封装<br/>策略模式+事务协调"]
        T5["🟡 工具算法层架构<br/>TextProcessor算法实现<br/>独立可测试的工具类"]
        T6["🟠 数据持久层架构<br/>SQLAlchemy ORM映射<br/>事务管理+批量优化"]
        T7["� 响应构建层架构<br/>JSON序列化处理<br/>自动连接管理"]
    end

    %% 中间：实现细节层（详细信息）
    subgraph "💻 实现细节层"
        I1["🔧 FastAPI路由实现<br/>@router.delete('/delete')<br/>async def delete_phrase(<br/>  text_info: TextInfoColorUpdate,<br/>  db: Session = Depends(get_db)<br/>):<br/>  service = PhraseService(db)<br/>  result = await service.delete_phrase(<br/>    text_info.color, text_info.text<br/>  )<br/>  return result"]

        I2["✂️ 文本切割算法详细实现<br/>TextProcessor.split_text_by_comma():<br/>parts = text.split('、')<br/>for i, part in enumerate(parts):<br/>  if i == len(parts) - 1:<br/>    if text.endswith('、'):<br/>      blocks.append(part) if part<br/>      tail_text = ''<br/>    else: tail_text = part<br/>  else: blocks.append(part) if part<br/>返回: TextSplitResult(blocks, tail_text)"]

        I3["🔍 差异检测算法详细实现<br/>TextProcessor.find_different_blocks():<br/>source_count = Counter(old_blocks)<br/>target_count = Counter(new_blocks)<br/>differences = []<br/>for block, source_num in source_count.items():<br/>  target_num = target_count.get(block, 0)<br/>  diff = source_num - target_num<br/>  if diff > 0:<br/>    differences.extend([block] * diff)<br/>返回: differences列表"]

        I4["🎯 删除策略判断实现<br/>deleted_block = deleted_blocks[0]<br/>has_digit_suffix = bool(<br/>  re.search(r'\\d$', deleted_block)<br/>)<br/>if not has_digit_suffix:<br/>  _handle_delete_without_digit()<br/>else:<br/>  _handle_delete_with_digit()<br/>正则模式: r'^(.+?)(\\d+)$'"]

        I5["🗑️ 简单删除实现<br/>_handle_delete_without_digit():<br/>self._delete_phrase_by_word_and_type(<br/>  deleted_block, 0, text_info_id<br/>)<br/>self._db.query(TextInfo)<br/>  .filter(TextInfo.id == text_info_id)<br/>  .update({'text': new_text})<br/>self._db.flush()"]

        I6["🔄 复杂删除重编号实现<br/>_handle_delete_with_digit():<br/>match = re.search(r'^(.+?)(\\d+)$', deleted_block)<br/>word = match.group(1)<br/>type_value = int(match.group(2))<br/>删除目标记录 + 查询需要重编号的phrase<br/>批量更新type和text字段<br/>事务提交"]

        I7["🗃️ 数据库操作SQL详细实现<br/>删除操作:<br/>DELETE FROM phrase WHERE<br/>  word=? AND type=? AND text_id=?<br/>查询操作:<br/>SELECT id,word,type,text_id FROM phrase<br/>  WHERE word=? AND type>? ORDER BY type ASC<br/>批量更新:<br/>UPDATE phrase SET type=? WHERE id=?<br/>UPDATE text_info SET text=? WHERE id=?"]

        I8["💾 事务管理详细实现<br/>try:<br/>  delete_result = 删除操作<br/>  update_result = 更新操作<br/>  self._db.commit()<br/>  return result<br/>except Exception as e:<br/>  self._db.rollback()<br/>  logger.error(f'Error: {str(e)}')<br/>  raise BusinessException(f'删除失败：{str(e)}')"]
    end

    %% 右侧：执行状态层（详细数据）
    subgraph "📊 执行状态层"
        R1["📥 HTTP请求数据<br/>POST /phrase/delete<br/>Headers: {'Content-Type': 'application/json'}<br/>Body: {<br/>  'color': 0,<br/>  'text': '苹果、香蕉、橙子、'<br/>}<br/>✅ Pydantic验证通过<br/>TextInfoColorUpdate(color=0, text='苹果、香蕉、橙子、')"]

        R2["🔄 文本处理过程数据<br/>old_text: '苹果、苹果2、香蕉、橙子、'<br/>old_blocks: ['苹果', '苹果2', '香蕉', '橙子']<br/>new_blocks: ['苹果', '香蕉', '橙子']<br/>differences: ['苹果2']<br/>has_digit_suffix: True<br/>match.group(1): '苹果'<br/>match.group(2): '2'"]

        R3["💾 数据库查询执行状态<br/>删除操作: DELETE FROM phrase<br/>  WHERE word='苹果' AND type=2 AND text_id=123<br/>  affected_rows = 1<br/>重编号查询: SELECT * FROM phrase<br/>  WHERE word='苹果' AND type>2 ORDER BY type<br/>  result_count = 2 (苹果3, 苹果4)<br/>批量更新: 2条phrase记录, 2条text_info记录"]

        R4["🔄 重编号处理状态<br/>phrase_list_to_update: [<br/>  Phrase(word='苹果', type=3, text_id=456),<br/>  Phrase(word='苹果', type=4, text_id=789)<br/>]<br/>更新映射: {<br/>  '苹果3' -> '苹果2',<br/>  '苹果4' -> '苹果3'<br/>}<br/>text_info更新: 2条记录文本替换"]

        R5["📤 HTTP响应数据<br/>Status: 200 OK<br/>Headers: {'Content-Type': 'application/json'}<br/>Body: {<br/>  'message': '删除成功',<br/>  'updated_text_infos': [<br/>    {'id': '123', 'color': 0, 'text': '苹果、香蕉、橙子、'},<br/>    {'id': '456', 'color': 1, 'text': '水果：苹果、苹果2'},<br/>    {'id': '789', 'color': 2, 'text': '苹果3变成苹果2'}<br/>  ]<br/>}"]

        R6["⚠️ 错误处理状态<br/>try-catch包装:<br/>BusinessException: 业务逻辑错误<br/>HTTPException: HTTP状态码400<br/>Database rollback: 事务回滚<br/>Logger记录: error级别日志<br/>响应格式: {'detail': '删除失败：具体错误信息'}"]
    end

    %% 业务概念层内部连接（垂直流动）
    B1 --> B2
    B2 --> B3
    B3 --> B4
    B4 --> B5
    B5 --> B6
    B6 --> B7

    %% 技术架构层内部连接（垂直流动）
    T1 --> T2
    T2 --> T3
    T3 --> T4
    T4 --> T5
    T5 --> T6
    T6 --> T7

    %% 主要跨层连接（水平流动）
    B1 --> T1
    B3 --> T4
    B5 --> T4
    B6 --> T5
    B7 --> T6
    T1 --> I1
    T4 --> I4
    T5 --> I2
    T6 --> I7

    %% 实现细节层内部流程（垂直流动，减少交叉）
    I1 --> I2
    I2 --> I3
    I3 --> I4
    I4 --> I5
    I4 --> I6
    I5 --> I7
    I6 --> I7
    I7 --> I8

    %% 执行状态层内部流程（垂直流动）
    R1 --> R2
    R2 --> R3
    R3 --> R4
    R4 --> R5

    %% 最少必要的跨层连接
    I8 --> R5
    R6 -.-> R5

    %% 样式定义
    classDef businessLayer fill:#e8f5e8,stroke:#4caf50,stroke-width:2px,color:#2e7d32
    classDef techLayer fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#1976d2
    classDef implLayer fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#f57c00
    classDef runtimeLayer fill:#fce4ec,stroke:#e91e63,stroke-width:2px,color:#c2185b

    class B1,B2,B3,B4,B5,B6,B7 businessLayer
    class T1,T2,T3,T4,T5,T6,T7 techLayer
    class I1,I2,I3,I4,I5,I6,I7,I8 implLayer
    class R1,R2,R3,R4,R5,R6 runtimeLayer
            </div>

            <!-- 业务概念层 -->
            <div class="layer-toggle" onclick="toggleLayer('business-layer')">
                🎯 业务概念层 - 点击展开/收起
            </div>
            <div id="business-layer" class="layer-content">
                <div class="business-flow">
                    <h4>📋 删除词汇业务流程</h4>
                    <p><strong>业务目标：</strong>从文本中删除指定词汇，并保持全局编号的连续性</p>
                    <p><strong>核心规则：</strong></p>
                    <ul>
                        <li>删除重复词汇时，优先删除编号较大的词汇</li>
                        <li>删除后，后续同词汇的编号需要前移补齐空缺</li>
                        <li>所有相关文本需要同步更新，保持全局一致性</li>
                        <li>操作需要支持事务回滚，确保数据完整性</li>
                    </ul>
                </div>

                <div class="business-flow">
                    <h4>🔄 业务处理步骤</h4>
                    <ol>
                        <li><strong>文本差异识别：</strong>比较修改前后的文本，找出被删除的词汇块</li>
                        <li><strong>删除策略判断：</strong>根据词汇是否带编号选择简单删除或复杂重编号</li>
                        <li><strong>编号重新分配：</strong>为剩余的同词汇重新分配连续编号</li>
                        <li><strong>全局文本同步：</strong>更新所有包含该词汇的文本记录</li>
                        <li><strong>结果验证确认：</strong>确保操作完成后数据的一致性和完整性</li>
                    </ol>
                </div>

                <div class="business-flow">
                    <h4>🏭 词汇服务初始化业务概念</h4>
                    <p><strong>业务含义：</strong>创建词汇管理服务实例，传入数据库会话进行初始化</p>
                    <p><strong>调用方式：</strong>service = PhraseService(db)，然后调用service.delete_phrase(color, text)</p>
                    <p><strong>业务价值：</strong>封装词汇管理的业务逻辑，提供统一的服务接口</p>
                </div>

                <div class="business-flow">
                    <h4>📞 文本处理服务调用业务概念</h4>
                    <p><strong>业务含义：</strong>调用文本处理工具进行文本切割和差异检测</p>
                    <p><strong>处理流程：</strong>split_text_by_comma(old_text) → split_text_by_comma(new_text) → find_different_blocks()</p>
                    <p><strong>业务价值：</strong>获取需要删除的词汇块，为后续删除策略提供数据基础</p>
                </div>

                <div class="business-flow">
                    <h4>🎯 删除策略选择业务概念</h4>
                    <p><strong>业务含义：</strong>遍历差异块，通过正则表达式模式匹配判断词汇是否带有数字后缀</p>
                    <p><strong>策略分支：</strong>if match: 复杂删除重编号 else: 简单删除</p>
                    <p><strong>业务价值：</strong>根据词汇特征选择最适合的删除处理方式</p>
                </div>

                <div class="business-flow">
                    <h4>🔄 重编号业务逻辑概念</h4>
                    <p><strong>业务含义：</strong>查询需要重编号的词汇列表，遍历每个词汇计算新编号</p>
                    <p><strong>处理逻辑：</strong>original_type → new_type = original_type - 1 → 构建映射关系</p>
                    <p><strong>业务价值：</strong>确保全局编号连续性，维护数据的逻辑一致性</p>
                </div>

                <div class="business-flow">
                    <h4>📝 全局文本同步逻辑概念</h4>
                    <p><strong>业务含义：</strong>收集所有受影响的文本记录，应用字符串替换规则更新文本内容</p>
                    <p><strong>同步范围：</strong>所有包含目标词汇的TextInfo记录都需要同步更新</p>
                    <p><strong>业务价值：</strong>保证全局数据一致性，避免数据孤岛问题</p>
                </div>

                <div class="business-flow">
                    <h4>📊 业务场景示例</h4>
                    <p><strong>场景：</strong>用户从"苹果、苹果2、苹果3、香蕉"中删除"苹果2"</p>
                    <p><strong>期望结果：</strong>"苹果、苹果2、香蕉"（原苹果3变成苹果2）</p>
                    <p><strong>影响范围：</strong>所有包含"苹果3"的文本都需要更新为"苹果2"</p>
                </div>
            </div>

            <!-- 技术架构层 -->
            <div class="layer-toggle" onclick="toggleLayer('tech-layer')">
                ⚙️ 技术架构层 - 点击展开/收起
            </div>
            <div id="tech-layer" class="layer-content">
                <div class="tech-component">
                    <h4>🏗️ 系统架构组件</h4>
                    <p><strong>接口层：</strong>FastAPI路由器 + Pydantic数据验证</p>
                    <p><strong>业务层：</strong>PhraseService词汇服务 + 业务规则处理</p>
                    <p><strong>工具层：</strong>TextProcessor文本处理器 + 算法实现</p>
                    <p><strong>数据层：</strong>SQLAlchemy ORM + 数据库事务管理</p>
                    <p><strong>配置层：</strong>数据库连接池 + 依赖注入容器</p>
                </div>

                <div class="tech-component">
                    <h4>🌐 HTTP接口层技术架构</h4>
                    <p><strong>前端请求：</strong>DELETE /phrase/delete，请求头application/json，请求体包含color和text字段</p>
                    <p><strong>FastAPI路由器：</strong>@router.delete('/delete')装饰器，参数解析text_info: TextInfoColorUpdate</p>
                    <p><strong>依赖注入：</strong>db: Session = Depends(get_db)，自动管理数据库会话生命周期</p>
                    <p><strong>技术特点：</strong>支持自动API文档生成，类型安全的参数验证</p>
                </div>

                <div class="tech-component">
                    <h4>🔧 配置管理层技术架构</h4>
                    <p><strong>数据库配置：</strong>app.config.database.py，DATABASE_URL配置，engine = create_engine(DATABASE_URL)</p>
                    <p><strong>会话工厂：</strong>SessionLocal = sessionmaker(engine)，创建数据库会话工厂</p>
                    <p><strong>会话管理：</strong>get_db()生成器函数，yield db返回会话，finally确保db.close()</p>
                    <p><strong>扩展支持：</strong>支持Redis会话、缓存配置等组件集成</p>
                </div>

                <div class="tech-component">
                    <h4>🔴 数据验证解析层技术架构</h4>
                    <p><strong>Pydantic验证：</strong>TextInfoColorUpdate验证模型，color字段范围0≤x≤8，text字段长度≤1000字符</p>
                    <p><strong>类型转换：</strong>JSON自动转换为Python对象，支持必填字段存在性检查</p>
                    <p><strong>验证结果：</strong>TextInfoColorUpdate(color=0, text='苹果、香蕉、橙子、')</p>
                    <p><strong>技术优势：</strong>基于Python类型注解，提供编译时类型检查</p>
                </div>

                <div class="tech-component">
                    <h4>🟣 业务逻辑层技术架构</h4>
                    <p><strong>服务封装：</strong>PhraseService类封装词汇管理业务逻辑，通过依赖注入获取数据库会话</p>
                    <p><strong>工具调用：</strong>TextProcessor.split_text_by_comma()和find_different_blocks()提供算法支持</p>
                    <p><strong>策略模式：</strong>根据词汇特征选择删除策略，支持简单删除和复杂重编号两种模式</p>
                    <p><strong>事务协调：</strong>统筹数据层操作执行，确保业务逻辑的原子性</p>
                </div>

                <div class="tech-component">
                    <h4>🟡 工具算法层技术架构</h4>
                    <p><strong>文本切割：</strong>split('、') + enumerate遍历，支持尾部文本特殊处理</p>
                    <p><strong>差异检测：</strong>Counter统计算法，计算source_count - target_count差异</p>
                    <p><strong>算法特点：</strong>独立于业务逻辑，支持单元测试，可复用性强</p>
                    <p><strong>返回结果：</strong>TextSplitResult(blocks, tail_text)和differences列表</p>
                </div>

                <div class="tech-component">
                    <h4>🟠 数据持久化层技术架构</h4>
                    <p><strong>ORM映射：</strong>SQLAlchemy提供对象关系映射，支持Phrase和TextInfo模型</p>
                    <p><strong>SQL操作：</strong>DELETE删除、SELECT查询、UPDATE批量更新，支持条件查询和排序</p>
                    <p><strong>事务管理：</strong>BEGIN TRANSACTION → 执行操作 → COMMIT/ROLLBACK</p>
                    <p><strong>性能优化：</strong>批量更新操作，精确的WHERE条件，合理的索引使用</p>
                </div>

                <div class="tech-component">
                    <h4>🟢 响应构建层技术架构</h4>
                    <p><strong>数据整理：</strong>收集业务层处理结果，格式化更新后的文本信息</p>
                    <p><strong>响应构建：</strong>构建标准JSON响应结构，包含message和updated_text_infos</p>
                    <p><strong>JSON序列化：</strong>JSONResponse自动序列化，大整数ID转换为字符串避免精度丢失</p>
                    <p><strong>连接管理：</strong>依赖注入自动关闭数据库连接，确保资源正确释放</p>
                </div>

                <div class="tech-component">
                    <h4>🔄 数据流设计模式</h4>
                    <p><strong>请求处理模式：</strong>HTTP请求 → 数据验证 → 业务处理 → 数据持久化 → 响应构建</p>
                    <p><strong>依赖注入模式：</strong>数据库会话通过FastAPI的Depends机制自动管理</p>
                    <p><strong>事务管理模式：</strong>业务操作包装在数据库事务中，支持自动回滚</p>
                    <p><strong>错误处理模式：</strong>分层异常处理，业务异常与技术异常分离</p>
                </div>

                <div class="tech-component">
                    <h4>📦 核心技术选型</h4>
                    <p><strong>Web框架：</strong>FastAPI - 高性能异步框架，自动API文档生成</p>
                    <p><strong>数据验证：</strong>Pydantic - 基于Python类型注解的数据验证</p>
                    <p><strong>ORM框架：</strong>SQLAlchemy - 成熟的Python ORM，支持复杂查询</p>
                    <p><strong>数据库：</strong>关系型数据库，支持ACID事务特性</p>
                </div>
            </div>

            <!-- 实现细节层 -->
            <div class="layer-toggle" onclick="toggleLayer('implementation-layer')">
                💻 实现细节层 - 点击展开/收起
            </div>
            <div id="implementation-layer" class="layer-content">
                <div class="code-detail">
                    <h4>🔧 FastAPI路由实现</h4>
                    <pre># app/routers/phrase.py
@router.delete("/delete")
async def delete_phrase(
    text_info: TextInfoColorUpdate,
    db: Session = Depends(get_db)
):
    """删除词汇"""
    try:
        service = PhraseService(db)
        result = await service.delete_phrase(
            text_info.color,
            text_info.text
        )
        return result
    except BusinessException as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )</pre>
                </div>

                <div class="code-detail">
                    <h4>🏗️ 依赖注入实现</h4>
                    <pre># app/config/database.py
def get_db():
    """数据库会话依赖"""
    db = SessionLocal()
    try:
        yield db  # 生成器，提供会话
    finally:
        db.close()  # 确保会话关闭

# 使用方式
db: Session = Depends(get_db)</pre>
                </div>

                <div class="code-detail">
                    <h4>✂️ 文本切割算法完整实现</h4>
                    <pre># app/utils/text_processor.py
@staticmethod
def split_text_by_comma(text: str) -> TextSplitResult:
    """按顿号分割文本，只保留顿号之间的文本块"""
    if not text:
        return TextSplitResult(blocks=[], tail_text="")

    parts = text.split("、")
    blocks = []
    tail_text = ""

    for i, part in enumerate(parts):
        part = part.strip()
        if i == len(parts) - 1:
            if text.endswith("、"):
                if part:
                    blocks.append(part)
                tail_text = ""
            else:
                tail_text = part
        else:
            if part:
                blocks.append(part)

    return TextSplitResult(blocks=blocks, tail_text=tail_text)</pre>
                </div>

                <div class="code-detail">
                    <h4>🔍 差异检测算法完整实现</h4>
                    <pre># app/utils/text_processor.py
@staticmethod
def find_different_blocks(source_blocks: List[str], target_blocks: List[str]) -> List[str]:
    """找出在source中存在但在target中不存在的文本块"""
    from collections import Counter

    source_count = Counter(source_blocks)
    target_count = Counter(target_blocks)

    differences = []
    for block, source_num in source_count.items():
        target_num = target_count.get(block, 0)
        diff = source_num - target_num
        differences.extend([block] * diff)

    return differences</pre>
                </div>

                <div class="code-detail">
                    <h4>🎯 删除策略判断实现</h4>
                    <pre># app/services/phrase_service.py - delete_phrase方法
# 步骤4: 删除策略选择 - 根据是否有数字后缀选择处理方式
deleted_block = deleted_blocks[0]  # 通常只有一个被删除的词汇
has_digit_suffix = bool(re.search(r'\d$', deleted_block))

if not has_digit_suffix:
    # 简单删除：无数字后缀的词汇（如"苹果"）
    self._handle_delete_without_digit(deleted_block, text_info_id, new_text)
    self._db.commit()
    return {"message": "删除成功"}
else:
    # 复杂删除：有数字后缀的词汇（如"苹果1"），需要重新编号
    updated_text_infos = self._handle_delete_with_digit(deleted_block, text_info_id, new_text)
    self._db.commit()
    return {
        "message": "删除成功",
        "updated_text_infos": updated_text_infos
    }</pre>
                </div>

                <div class="code-detail">
                    <h4>� 复杂删除重编号详细实现</h4>
                    <pre># app/services/phrase_service.py - _handle_delete_with_digit方法
def _handle_delete_with_digit(self, deleted_block: str, text_info_id: int, new_text: str):
    # 步骤1: 解析词汇和数字后缀
    match = re.search(r'^(.+?)(\d+)$', deleted_block)
    if match:
        word = match.group(1)  # 提取词汇部分（如"苹果"）
        type_value = int(match.group(2))  # 提取数字部分（如1）

        # 步骤2: 删除目标Phrase记录
        self._delete_phrase_by_word_and_type(word, type_value, text_info_id)

        # 步骤3: 立即更新当前TextInfo（使用前端传入的new_text）
        self._db.query(TextInfo).filter(TextInfo.id == text_info_id).update({"text": new_text})
        self._db.flush()  # 确保更新立即生效

        # 步骤4: 查找需要重新编号的相关词汇（全局查询，按type升序排列）
        phrase_list_to_update = self._db.query(Phrase).filter(
            Phrase.word == word,  # 相同词汇
            Phrase.type > type_value  # type大于被删除的type
        ).order_by(Phrase.type.asc()).all()  # 按type从小到大排序，确保正确的重新编号

        # 步骤5: 批量重新编号处理
        if phrase_list_to_update:
            # 5.1 获取所有相关的TextInfo对象
            text_ids = list(set([phrase.text_id for phrase in phrase_list_to_update]))
            text_infos = self._db.query(TextInfo).filter(TextInfo.id.in_(text_ids)).all()
            text_info_dict = {text_info.id: text_info for text_info in text_infos}

            # 5.2 遍历需要更新的phrase（已按type从小到大排序）
            for phrase in phrase_list_to_update:
                # 构建旧的词汇文本（如"苹果2"）
                original_type = phrase.type
                old_word = phrase.word + str(original_type)

                # 计算新的type值（减1，因为删除了一个更小的type）
                new_type = original_type - 1
                new_word = phrase.word + (str(new_type) if new_type > 0 else "")

                # 在对应的TextInfo中更新词汇文本
                text_info = text_info_dict.get(phrase.text_id)
                if text_info:
                    # 替换文本中的词汇（如"苹果2" -> "苹果1"）
                    current_text = text_info.text or ""
                    new_word_text = current_text.replace(old_word, new_word)
                    text_info.text = new_word_text

                # 更新phrase的type值
                phrase.type = new_type

            # 5.3 批量提交数据库更新
            self._update_phrases_batch(phrase_list_to_update)  # 更新Phrase表

            self._db.bulk_update_mappings(TextInfo, [  # 批量更新TextInfo表
                {"id": text_info.id, "text": text_info.text}
                for text_info in text_infos
            ])</pre>
                </div>

                <div class="code-detail">
                    <h4>�🗃️ 数据库操作SQL详细实现</h4>
                    <pre># 删除目标词汇 - _delete_phrase_by_word_and_type
DELETE FROM phrase
WHERE word = 'apple' AND type = 2 AND text_id = 123

# 查询需要重编号的词汇 - 全局查询
SELECT id, word, type, text_id FROM phrase
WHERE word = 'apple' AND type > 2
ORDER BY type ASC

# 批量更新phrase的type - _update_phrases_batch
UPDATE phrase SET type = 2 WHERE id = 456
UPDATE phrase SET type = 3 WHERE id = 789

# 批量更新text_info的text - bulk_update_mappings
UPDATE text_info SET text = '苹果、苹果2、香蕉' WHERE id = 456
UPDATE text_info SET text = '水果包括苹果3' WHERE id = 789

# 事务控制
BEGIN TRANSACTION
-- 所有操作
COMMIT
-- 或者在异常时
ROLLBACK</pre>
                </div>
            </div>

            <!-- 执行状态层 -->
            <div class="layer-toggle" onclick="toggleLayer('runtime-layer')">
                📊 执行状态层 - 点击展开/收起
            </div>
            <div id="runtime-layer" class="layer-content">
                <div class="runtime-data">
                    <h4>📥 输入数据示例</h4>
                    <pre>{
  "color": 0,
  "text": "苹果、香蕉、橙子、"
}</pre>
                    <p><strong>数据验证结果：</strong>✅ 通过Pydantic验证</p>
                    <p><strong>解析后对象：</strong>TextInfoColorUpdate实例</p>
                </div>

                <div class="runtime-data">
                    <h4>🔄 处理过程数据</h4>
                    <p><strong>文本切割结果：</strong>blocks=['苹果', '香蕉', '橙子'], tail_text=''</p>
                    <p><strong>差异检测结果：</strong>differences=['苹果2'] (假设删除了苹果2)</p>
                    <p><strong>模式匹配结果：</strong>word='苹果', type=2</p>
                    <p><strong>查询结果：</strong>找到2条需要重编号的记录</p>
                </div>

                <div class="runtime-data">
                    <h4>💾 数据库执行状态</h4>
                    <p><strong>删除操作：</strong>affected_rows = 1</p>
                    <p><strong>查询操作：</strong>返回2条Phrase记录</p>
                    <p><strong>批量更新：</strong>phrase_updated = 2, text_updated = 2</p>
                    <p><strong>事务状态：</strong>✅ 成功提交</p>
                </div>

                <div class="runtime-data">
                    <h4>📤 输出响应示例</h4>
                    <pre>{
  "message": "删除成功",
  "updated_text_infos": [
    {
      "id": "1234567890123456789",
      "color": 0,
      "text": "苹果、苹果2、香蕉、橙子、"
    },
    {
      "id": "9876543210987654321",
      "color": 1,
      "text": "水果包括苹果、苹果2等"
    }
  ]
}</pre>
                </div>
            </div>

            <script>
                function toggleLayer(layerId) {
                    const content = document.getElementById(layerId);
                    const toggle = content.previousElementSibling;

                    if (content.classList.contains('active')) {
                        content.classList.remove('active');
                        toggle.classList.remove('active');
                    } else {
                        content.classList.add('active');
                        toggle.classList.add('active');
                    }
                }
            </script>
        </div>
    </div>

</body>
</html>
