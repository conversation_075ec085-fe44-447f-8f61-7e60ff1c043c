import time
import threading
from typing import Optional

class IdGenerator:
    """ID生成器 - 基于雪花算法的Python实现"""

    # 起始时间戳（2020-01-01 00:00:00）
    _START_TIMESTAMP = 1577808000000

    _SEQUENCE_BITS = 12      # 序列号位数（0~4095）
    _MACHINE_BITS = 5        # 机器标识位数（0~31）
    _DATACENTER_BITS = 5     # 数据中心标识位数（0~31）

    _MAX_DATACENTER_NUM = ~(-1 << _DATACENTER_BITS)
    _MAX_MACHINE_NUM = ~(-1 << _MACHINE_BITS)
    _MAX_SEQUENCE = ~(-1 << _SEQUENCE_BITS)

    _MACHINE_LEFT = _SEQUENCE_BITS
    _DATACENTER_LEFT = _SEQUENCE_BITS + _MACHINE_BITS
    _TIMESTAMP_LEFT = _DATACENTER_LEFT + _DATACENTER_BITS

    def __init__(self, datacenter_id: int = 1, machine_id: int = 1):
        if not (0 <= datacenter_id <= self._MAX_DATACENTER_NUM):
            raise ValueError(f"datacenter_id must be between 0 and {self._MAX_DATACENTER_NUM}")
        if not (0 <= machine_id <= self._MAX_MACHINE_NUM):
            raise ValueError(f"machine_id must be between 0 and {self._MAX_MACHINE_NUM}")

        self._datacenter_id = datacenter_id
        self._machine_id = machine_id
        self._sequence = 0
        self._last_timestamp = -1
        self._lock = threading.Lock()

    def generate_id(self) -> int:
        """生成下一个唯一ID"""
        with self._lock:
            current_timestamp = int(time.time() * 1000)

            if current_timestamp < self._last_timestamp:
                raise RuntimeError("Clock moved backwards. Refusing to generate id")

            if current_timestamp == self._last_timestamp:
                self._sequence = (self._sequence + 1) & self._MAX_SEQUENCE
                if self._sequence == 0:
                    current_timestamp = self._wait_next_millis(current_timestamp)
            else:
                self._sequence = 0

            self._last_timestamp = current_timestamp

            return ((current_timestamp - self._START_TIMESTAMP) << self._TIMESTAMP_LEFT) | \
                   (self._datacenter_id << self._DATACENTER_LEFT) | \
                   (self._machine_id << self._MACHINE_LEFT) | \
                   self._sequence

    def _wait_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = int(time.time() * 1000)
        while timestamp <= last_timestamp:
            timestamp = int(time.time() * 1000)
        return timestamp

# 全局ID生成器实例
_id_generator: Optional[IdGenerator] = None

def get_id_generator() -> IdGenerator:
    """获取全局ID生成器实例"""
    global _id_generator
    if _id_generator is None:
        _id_generator = IdGenerator()
    return _id_generator

def generate_id() -> int:
    """生成唯一ID的便捷函数"""
    return get_id_generator().generate_id()
