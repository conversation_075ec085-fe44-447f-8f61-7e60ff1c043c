from typing import List, Dict, NamedTuple
from dataclasses import dataclass

@dataclass
class TextSplitResult:
    """文本分割结果"""
    blocks: List[str]
    tail_text: str

class TextProcessor:
    """文本处理工具类 - Python化版本"""

    @staticmethod
    def split_text_by_comma(text: str) -> TextSplitResult:
        """
        按顿号分割文本，只保留顿号之间的文本块

        Args:
            text: 原始文本

        Returns:
            TextSplitResult: 包含blocks和tail_text的结果对象
        """
        if not text:
            return TextSplitResult(blocks=[], tail_text="")

        # 按顿号分割
        parts = text.split("、")
        blocks = []
        tail_text = ""

        # 处理分割后的各部分
        for i, part in enumerate(parts):
            part = part.strip()
            if i == len(parts) - 1:
                # 最后一个部分
                if text.endswith("、"):
                    # 如果原文本以顿号结尾，这个part是正常的文本块
                    if part:
                        blocks.append(part)
                    tail_text = ""
                else:
                    # 如果原文本不以顿号结尾，这个part是tail_text
                    tail_text = part
            else:
                # 不是最后一个部分，都是正常的文本块
                if part:
                    blocks.append(part)

        return TextSplitResult(blocks=blocks, tail_text=tail_text)

    @staticmethod
    def find_different_blocks(source_blocks: List[str], target_blocks: List[str]) -> List[str]:
        """
        找出在source中存在但在target中不存在的文本块

        Args:
            source_blocks: 源文本块列表
            target_blocks: 目标文本块列表

        Returns:
            差异文本块列表
        """
        from collections import Counter

        # 使用Counter统计更Pythonic
        source_count = Counter(source_blocks)
        target_count = Counter(target_blocks)

        # 找出source中多出来的文本块
        differences = []
        for block, source_num in source_count.items():
            target_num = target_count.get(block, 0)

            # 如果source中的数量大于target中的数量，则有差异
            diff = source_num - target_num
            differences.extend([block] * diff)

        return differences

    @staticmethod
    def get_block_index_map(text_blocks: List[str], diff_blocks: List[str]) -> Dict[str, int]:
        """
        获取文本块在原文本中的位置索引
        优化：优先选择后面出现的相同文本块作为新增

        Args:
            text_blocks: 文本块列表
            diff_blocks: 差异文本块列表

        Returns:
            文本块及其在原列表中的索引映射
            格式说明：key="文本内容_序号", value=索引位置
            例如：{"你好_0" -> 1} 表示第1个差异的"你好"在索引位置1
        """
        index_map = {}
        used_count = {}

        for diff_block in diff_blocks:  
            used_times = used_count.get(diff_block, 0)

            # 收集所有匹配的索引位置 - 使用列表推导式更Pythonic
            matching_indices = [i for i, block in enumerate(text_blocks) if block == diff_block]

            # 优先选择后面的索引：从后往前选择第used_times+1个
            if len(matching_indices) > used_times:
                selected_index = matching_indices[-(used_times + 1)]
                # key格式：文本内容_序号，value：索引位置
                index_map[f"{diff_block}_{used_times}"] = selected_index

            used_count[diff_block] = used_times + 1

        return index_map
