from fastapi import HTTPException
from typing import Any, Dict

class BusinessException(HTTPException):
    """业务异常类"""
    def __init__(self, detail: str, status_code: int = 400):
        super().__init__(status_code=status_code, detail=detail)

def create_error_response(message: str, status_code: int = 400) -> HTTPException:
    """创建错误响应"""
    return HTTPException(status_code=status_code, detail=message)

def create_success_response(data: Any = None) -> Dict[str, Any]:
    """创建成功响应"""
    if data is None:
        return {"message": "success"}
    return data if isinstance(data, dict) else {"data": data}
