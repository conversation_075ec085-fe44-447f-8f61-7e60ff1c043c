from sqlalchemy import Column, BigInteger, String, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app.config.database import Base

class Coordinate(Base):
    __tablename__ = "coordinate"  # 保持与Java版本一致

    id = Column(BigInteger, primary_key=True, index=True)
    table_id = Column("tableId", BigInteger, ForeignKey("table_info.id"), nullable=False, index=True)  # 保持Java字段名
    color = Column(Integer, nullable=False)
    position = Column(String(255), nullable=False)
    voc = Column(String(255), nullable=True)  # 保持与Java一致的字段名
    repeated = Column(Integer, nullable=False, default=0)  # 保持与Java一致的字段名

    # 关系定义
    table = relationship("Table", back_populates="coordinates")

    def __repr__(self) -> str:
        return f"<Coordinate(id={self.id}, position='{self.position}', color={self.color})>"
