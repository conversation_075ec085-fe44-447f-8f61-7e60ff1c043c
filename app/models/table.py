from sqlalchemy import Column, BigInteger, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.config.database import Base
from datetime import datetime

class Table(Base):
    __tablename__ = "table_info"  

    id = Column(BigInteger, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    create_time = Column(DateTime, default=func.now(), nullable=False)  

    # 关系定义 - 移除与TextInfo和Phrase的关系
    coordinates = relationship("Coordinate", back_populates="table", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Table(id={self.id}, name='{self.name}')>"
