from sqlalchemy import Column, BigInteger, String, Integer, ForeignKey
from sqlalchemy.orm import relationship
from app.config.database import Base

class Phrase(Base):
    __tablename__ = "phrase"

    id = Column(BigInteger, primary_key=True, index=True)
    text_id = Column("textId", BigInteger, ForeignKey("text_info.id"), nullable=False, index=True)
    word = Column(String(255), nullable=False)
    type = Column(Integer, nullable=False)

    # 关系定义 - 移除与table的关系
    text_info = relationship("TextInfo", back_populates="phrases")

    def __repr__(self) -> str:
        return f"<Phrase(id={self.id}, text_id={self.text_id}, word='{self.word}', type={self.type})>"
