from sqlalchemy import Column, BigInteger, String, Integer
from sqlalchemy.orm import relationship
from app.config.database import Base

class TextInfo(Base):
    __tablename__ = "text_info"

    id = Column(BigInteger, primary_key=True, index=True)
    color = Column(Integer, nullable=False, unique=True)  # 颜色标识，0-8，唯一
    text = Column(String, nullable=True, default="")

    # 关系定义 - 移除与table的关系
    phrases = relationship("Phrase", back_populates="text_info", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<TextInfo(id={self.id}, color={self.color})>"
