from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.services.coordinate_service import CoordinateService
from app.schemas.coordinate import CoordinateUpdate
from app.utils.exceptions import BusinessException
from typing import List, Dict, Any

router = APIRouter(prefix="/coordinate", tags=["coordinates"])

@router.get("/batch")
async def batch_import_coordinates(id: int, db: Session = Depends(get_db)):
    """批量导入坐标数据"""
    try:
        service = CoordinateService(db)
        coordinates = await service.batch_import_coordinates(id)
        return {"coordinates": coordinates, "total": len(coordinates)}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.delete("/delete")
async def delete_coordinates(id: int, db: Session = Depends(get_db)):
    """删除表格的所有坐标数据"""
    try:
        service = CoordinateService(db)
        await service.delete_coordinates_by_table(id)
        return {"message": "删除成功"}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.get("/find")
async def get_coordinates(id: int, db: Session = Depends(get_db)):
    """查询表格的坐标数据"""
    try:
        service = CoordinateService(db)
        coordinates = await service.get_coordinates_by_table(id)
        return {"coordinates": coordinates, "total": len(coordinates)}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.get("/list")
async def get_phrases_by_coordinate(color: int, table_id: int, coordinate_id: int, db: Session = Depends(get_db)):
    """查询坐标关联的词汇列表"""
    try:
        service = CoordinateService(db)
        phrases = await service.get_phrases_by_coordinate(color, table_id, coordinate_id)
        return {"phrases": phrases, "total": len(phrases)}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.put("/update")
async def update_coordinate(coordinate: CoordinateUpdate, db: Session = Depends(get_db)):
    """更新坐标信息"""
    try:
        service = CoordinateService(db)
        updated_coordinates = await service.update_coordinate(coordinate.id, coordinate.model_dump(exclude={"id"}))
        return {"coordinates": updated_coordinates}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
