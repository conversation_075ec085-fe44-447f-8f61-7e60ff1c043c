from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.services.phrase_service import PhraseService
from app.schemas.text_info import TextInfoColorUpdate
from app.schemas.phrase import PhraseListResponse
from app.utils.exceptions import BusinessException

router = APIRouter(prefix="/phrase", tags=["phrases"])

@router.post("/add")
async def add_phrase(text_info: TextInfoColorUpdate, db: Session = Depends(get_db)):
    """添加词汇"""
    try:
        service = PhraseService(db)
        result = await service.add_phrase(text_info.color, text_info.text)
        return result
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.delete("/delete")
async def delete_phrase(text_info: TextInfoColorUpdate, db: Session = Depends(get_db)):
    """删除词汇"""
    try:
        service = PhraseService(db)
        result = await service.delete_phrase(text_info.color, text_info.text)
        return result
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.get("/list", response_model=PhraseListResponse)
async def get_phrases_by_color(color: int, db: Session = Depends(get_db)):
    """根据颜色查询词汇列表"""
    try:
        service = PhraseService(db)
        phrases = await service.get_phrases_by_color(color)
        return PhraseListResponse(phrases=phrases, total=len(phrases))
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
