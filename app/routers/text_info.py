from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.services.text_info_service import TextInfoService
from app.schemas.text_info import TextInfoUpdate, TextInfoResponse
from app.utils.exceptions import BusinessException
from typing import List

router = APIRouter(prefix="/text", tags=["text_info"])

@router.get("/find", response_model=List[TextInfoResponse])
async def get_text_infos(db: Session = Depends(get_db)):
    """查询所有文本信息"""
    try:
        service = TextInfoService(db)
        text_infos = await service.get_all_text_infos()
        return [TextInfoResponse.model_validate(text_info) for text_info in text_infos]
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.put("/update", response_model=TextInfoResponse)
async def update_text_info(text_info: TextInfoUpdate, db: Session = Depends(get_db)):
    """更新文本信息"""
    try:
        service = TextInfoService(db)
        # 使用新的更新方法，通过ID进行精确更新
        updated_text_info = await service.update_text_info_by_data(text_info.model_dump())
        return TextInfoResponse.model_validate(updated_text_info)
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
