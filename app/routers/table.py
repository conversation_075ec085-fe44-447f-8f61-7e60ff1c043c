from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.services.table_service import TableService
from app.schemas.table import TableCreate, TableUpdate, TableResponse, TableListResponse
from app.utils.exceptions import BusinessException

router = APIRouter(prefix="/table", tags=["tables"])

@router.post("/add", response_model=TableResponse)
async def create_table(table_data: TableCreate, db: Session = Depends(get_db)):
    """创建表格"""
    try:
        table_service = TableService(db)

        # 创建表格
        table = await table_service.create_table(table_data.name)

        return TableResponse.model_validate(table)
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.get("/page", response_model=TableListResponse)
async def get_tables(db: Session = Depends(get_db)):
    """获取表格列表"""
    try:
        service = TableService(db)
        tables = await service.get_all_tables()

        return TableListResponse(
            tables=[TableResponse.model_validate(table) for table in tables],
            total=len(tables)
        )
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.put("/update")
async def update_table(table_data: TableUpdate, db: Session = Depends(get_db)):
    """更新表格"""
    try:
        service = TableService(db)
        await service.update_table(table_data.id, table_data.name)
        return {"message": "更新成功"}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@router.delete("/delete")
async def delete_table(id: int, db: Session = Depends(get_db)):
    """删除表格及其关联的坐标数据"""
    try:
        table_service = TableService(db)

        # 删除表格（会级联删除关联的坐标数据）
        await table_service.delete_table(id)

        return {"message": "删除成功"}
    except BusinessException as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
