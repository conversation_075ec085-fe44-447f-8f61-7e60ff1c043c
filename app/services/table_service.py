from sqlalchemy.orm import Session
from sqlalchemy import asc
from app.models.table import Table
from app.utils.id_generator import generate_id
from app.utils.exceptions import BusinessException
from datetime import datetime
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

class TableService:
    """表格服务类 - Python化版本，保持业务逻辑一致"""

    def __init__(self, db: Session):
        self._db = db

    async def create_table(self, name: str) -> Table:
        """创建表格 - 对应Java的addTable方法"""
        try:
            table = Table(
                id=generate_id(),
                name=name,
                create_time=datetime.now()
            )
            logger.info(f"Creating table at: {table.create_time}")

            self._db.add(table)
            self._db.commit()
            self._db.refresh(table)
            return table
        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"创建表格失败: {str(e)}")

    async def get_all_tables(self) -> List[Table]:
        """获取所有表格 - 对应Java的findPage方法"""
        try:
            return self._db.query(Table).order_by(asc(Table.create_time)).all()
        except Exception as e:
            raise BusinessException(f"查询表格失败: {str(e)}")

    async def update_table(self, table_id: int, name: str) -> None:
        """更新表格 - 对应Java的update方法"""
        try:
            table = self._db.query(Table).filter(Table.id == table_id).first()
            if not table:
                raise BusinessException("表格不存在")

            table.name = name
            self._db.commit()
        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"更新表格失败: {str(e)}")

    async def delete_table(self, table_id: int) -> None:
        """删除表格 - 对应Java的delete方法"""
        try:
            deleted_count = self._db.query(Table).filter(Table.id == table_id).delete()
            if deleted_count == 0:
                raise BusinessException("表格不存在")
            self._db.commit()
        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"删除表格失败: {str(e)}")
