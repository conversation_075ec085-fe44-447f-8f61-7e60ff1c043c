from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.coordinate import Coordinate
from app.models.text_info import TextInfo
from app.models.phrase import Phrase
from app.utils.id_generator import generate_id
from app.utils.exceptions import BusinessException
import re
import os
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class CoordinateService:
    """坐标服务类 - Python化版本，保持业务逻辑一致"""

    def __init__(self, db: Session):
        self._db = db
        # 批量插入配置
        self._batch_enabled = True
        self._batch_size = 1000
        self._sleep_ms = 100

    async def batch_import_coordinates(self, table_id: int) -> List[Dict[str, Any]]:
        """批量导入坐标数据 - 对应Java的batch方法"""
        try:
            # 直接在这里读取cor.txt文件并一次性完成所有操作
            coordinates = self._read_cor_file_and_create_coordinates(table_id)

            if not coordinates:
                raise BusinessException("从cor.txt文件读取数据失败，没有生成坐标数据")

            # 分批插入数据库，减少数据库压力
            total_inserted = self._insert_coordinates_in_batches(coordinates)

            

            # 返回完整的坐标数据供前端使用
            return [
                {
                    "id": coord.id,
                    "table_id": coord.table_id,
                    "color": coord.color,
                    "position": coord.position,
                    "voc": coord.voc,
                    "repeated": coord.repeated
                }
                for coord in coordinates
            ]

        except Exception as e:
            logger.error(f"批量插入坐标数据失败，tableId: {table_id}, 错误信息: {str(e)}")
            raise BusinessException(f"批量插入坐标数据失败: {str(e)}")

    async def delete_coordinates_by_table(self, table_id: int) -> None:
        """删除表格的所有坐标数据"""
        try:
            deleted_count = self._db.query(Coordinate).filter(Coordinate.table_id == table_id).delete()
            self._db.commit()
            logger.info(f"删除了 {deleted_count} 个坐标记录，table_id: {table_id}")
        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"删除失败：{str(e)}")

    async def get_coordinates_by_table(self, table_id: int) -> List[Dict[str, Any]]:
        """查询表格的坐标数据"""
        try:
            coordinates = self._db.query(Coordinate).filter(Coordinate.table_id == table_id).all()
            return [
                {
                    "id": coordinate.id,
                    "table_id": coordinate.table_id,
                    "color": coordinate.color,
                    "position": coordinate.position,
                    "voc": coordinate.voc,
                    "repeated": coordinate.repeated
                }
                for coordinate in coordinates
            ]
        except Exception as e:
            raise BusinessException(f"查询失败：{str(e)}")

    async def get_phrases_by_coordinate(self, color: int, table_id: int, coordinate_id: int) -> List[Dict[str, Any]]:
        """根据坐标查询关联的词汇列表 - 对应Java的listPhrase方法"""
        try:
            # 1. 根据 tableId 和 color 查找 TextInfo 记录
            text_info = self._db.query(TextInfo).filter(
                and_(TextInfo.table_id == table_id, TextInfo.color == color)
            ).first()

            if not text_info:
                raise BusinessException("未找到对应的文本信息")

            # 2. 根据 textId 查找对应的 Phrase 列表
            phrase_list = self._db.query(Phrase).filter(Phrase.text_id == text_info.id).all()

            logger.info(f"成功查询到Phrase列表，tableId: {table_id}, color: {color}, textId: {text_info.id}, 数量: {len(phrase_list)}")

            # 3. 返回 Phrase 列表给前端
            return [
                {
                    "id": phrase.id,
                    "text_id": phrase.text_id,
                    "table_id": phrase.table_id,
                    "word": phrase.word,
                    "type": phrase.type
                }
                for phrase in phrase_list
            ]

        except Exception as e:
            raise BusinessException(f"查询失败：{str(e)}")

    async def update_coordinate(self, coordinate_id: int, coordinate_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """更新坐标信息 - 对应Java的update方法，简化版本"""
        try:
            # 1. 获取要更新的坐标
            coordinate = self._db.query(Coordinate).filter(Coordinate.id == coordinate_id).first()
            if not coordinate:
                raise BusinessException("坐标不存在")

            old_voc = coordinate.voc

            # 2. 更新坐标信息
            coordinate.table_id = coordinate_data.get("table_id", coordinate.table_id)
            coordinate.color = coordinate_data.get("color", coordinate.color)
            coordinate.position = coordinate_data.get("position", coordinate.position)
            coordinate.voc = coordinate_data.get("voc", "")

            # 3. 计算repeated值
            if coordinate.voc:
                # 查询相同voc和color的坐标数量
                same_voc_count = self._db.query(Coordinate).filter(
                    and_(
                        Coordinate.table_id == coordinate.table_id,
                        Coordinate.voc == coordinate.voc,
                        Coordinate.color == coordinate.color
                    )
                ).count()
                coordinate.repeated = same_voc_count
            else:
                coordinate.repeated = 0

            self._db.commit()

            # 4. 如果voc发生变化，需要更新相关坐标的repeated值
            if old_voc != coordinate.voc and old_voc:
                self._update_repeated_values_after_voc_change(coordinate.table_id, old_voc, coordinate.color)

            # 5. 返回更新后的坐标信息
            return [{
                "id": coordinate.id,
                "table_id": coordinate.table_id,
                "color": coordinate.color,
                "position": coordinate.position,
                "voc": coordinate.voc,
                "repeated": coordinate.repeated
            }]

        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"更新失败：{str(e)}")

    def _update_repeated_values_after_voc_change(self, table_id: int, old_voc: str, color: int) -> None:
        """更新voc变化后的repeated值"""
        coordinates = self._db.query(Coordinate).filter(
            and_(
                Coordinate.table_id == table_id,
                Coordinate.voc == old_voc,
                Coordinate.color == color
            )
        ).order_by(Coordinate.repeated.asc()).all()

        for i, coord in enumerate(coordinates):
            coord.repeated = i

        self._db.commit()

    def _read_cor_file_and_create_coordinates(self, table_id: int) -> List[Coordinate]:
        """从cor.txt文件读取数据并创建坐标对象"""
        coordinates = []

        try:
            # 正则表达式匹配坐标格式：（x， y）  color
            pattern = re.compile(r'（(-?\d+)，\s*(-?\d+)）\s+(\d+)')

            # 读取项目根目录下的cor.txt文件
            cor_file_path = os.path.join(os.getcwd(), "cor.txt")

            with open(cor_file_path, 'r', encoding='utf-8') as file:
                for line in file:
                    line = line.strip()
                    if not line:
                        continue  # 跳过空行

                    match = pattern.match(line)
                    if match:
                        x = int(match.group(1))
                        y = int(match.group(2))
                        color_number = int(match.group(3))

                        # 创建Coordinate对象
                        coordinate = Coordinate(
                            id=generate_id(),
                            table_id=table_id,
                            color=color_number,
                            position=f"（{x}， {y}）",
                            voc="",
                            repeated=0
                        )
                        coordinates.append(coordinate)

            

        except IOError as e:
            logger.error(f"读取cor.txt文件失败: {str(e)}")
            raise BusinessException(f"读取cor.txt文件失败: {str(e)}")

        return coordinates

    def _insert_coordinates_in_batches(self, coordinates: List[Coordinate]) -> int:
        """分批插入坐标数据，减少数据库压力"""
        try:
            # 如果禁用分批插入，则一次性插入所有数据
            if not self._batch_enabled:
                
                self._db.add_all(coordinates)
                self._db.commit()
                return len(coordinates)

            total_inserted = 0
            total_batches = (len(coordinates) + self._batch_size - 1) // self._batch_size
            

            for i in range(0, len(coordinates), self._batch_size):
                end_index = min(i + self._batch_size, len(coordinates))
                batch = coordinates[i:end_index]

                try:
                    batch_number = (i // self._batch_size) + 1
                    logger.debug(f"执行第 {batch_number}/{total_batches} 批插入，数据量: {len(batch)}")

                    self._db.add_all(batch)
                    self._db.commit()
                    total_inserted += len(batch)

                    logger.debug(f"第 {batch_number} 批插入成功，插入 {len(batch)} 条数据")

                    # 批次间休息，减少数据库压力
                    if i + self._batch_size < len(coordinates) and self._sleep_ms > 0:
                        import time
                        time.sleep(self._sleep_ms / 1000.0)

                except Exception as e:
                    self._db.rollback()
                    logger.error(f"第 {batch_number} 批插入失败: {str(e)}")
                    raise e

            logger.info(f"分批插入完成，总插入数量: {total_inserted}")
            return total_inserted

        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"批量插入失败: {str(e)}")
