from sqlalchemy.orm import Session
from app.models.phrase import Phrase
from app.models.text_info import TextInfo
from app.schemas.phrase import PhraseResponse
from app.utils.text_processor import TextProcessor
from app.utils.id_generator import generate_id
from app.utils.exceptions import BusinessException
import re
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class PhraseService:
    """词汇服务类 - Python化版本，保持复杂业务逻辑一致"""

    def __init__(self, db: Session):
        self._db = db

    def _extract_word_from_block(self, block: str) -> str:
        """
        从文本块中提取纯词汇内容
        支持两种格式：
        1. "你好_0" -> "你好" (下划线分隔格式，来自block_index_map)
        2. "你好3" -> "你好" (末尾数字格式，来自实际文本块)
        """
        # 首先尝试下划线分隔格式
        if "_" in block:
            return block.rsplit("_", 1)[0]

        # 然后尝试末尾数字格式
        match = re.search(r'^(.+?)(\d+)$', block)
        if match:
            return match.group(1)

        # 如果都不匹配，返回原始内容
        return block

    # 私有方法 - 移除table_id相关逻辑
    def _get_phrases_by_word_and_text(self, word: str, text_id: int) -> List[Phrase]:
        """根据word和text_id查询Phrase列表"""
        return self._db.query(Phrase).filter(
            Phrase.word == word,
            Phrase.text_id == text_id
        ).all()

    def _delete_phrase_by_word_and_type(self, word: str, phrase_type: int, text_id: int) -> int:
        """根据word、type和text_id删除Phrase，返回删除数量"""
        return self._db.query(Phrase).filter(
            Phrase.word == word,
            Phrase.type == phrase_type,
            Phrase.text_id == text_id
        ).delete()

    def _get_phrases_by_word_and_type_greater_than(self, word: str, phrase_type: int, text_id: int) -> List[Phrase]:
        """查询word相同且type大于指定值的Phrase列表，按type升序排序"""
        return self._db.query(Phrase).filter(
            Phrase.word == word,
            Phrase.type > phrase_type,
            Phrase.text_id == text_id
        ).order_by(Phrase.type.asc()).all()

    def _save_phrases_batch(self, phrases: List[Phrase]) -> None:
        """批量保存Phrase"""
        self._db.add_all(phrases)

    def _update_phrases_batch(self, phrases: List[Phrase]) -> None:
        """批量更新Phrase"""
        for phrase in phrases:
            self._db.merge(phrase)
    
    async def add_phrase(self, color: int, new_text: str) -> Dict[str, Any]:
        """
        添加词汇 - 智能词汇添加与重复编号

        功能说明：
        1. 通过比较新旧文本，识别新增的词汇
        2. 自动处理重复词汇的编号
        3. 返回更新后的完整TextInfo对象

        Args:
            color: 颜色标识，用于定位TextInfo
            new_text: 前端传入的新文本内容

        Returns:
            Dict[str, Any]: 操作结果
            - 成功: {"message": "添加成功", "text_info": TextInfo对象}
            - 无新增: {"message": "没有新增词汇", "text_info": TextInfo对象}
        """
        try:
            # 1. 通过color获取textInfo的对应文本oldText，根据顿号切割文本块
            old_text_info = self._db.query(TextInfo).filter(TextInfo.color == color).first()
            if not old_text_info:
                raise BusinessException("文本信息不存在")

            old_text = old_text_info.text or ""
            text_info_id = old_text_info.id

            # 切割旧文本，只保留顿号之间的文本块
            old_split_result = TextProcessor.split_text_by_comma(old_text)
            old_blocks = old_split_result.blocks

            # 2. 取出前端的textInfo文本newText，切割好文本块后构成新文本块列表，以及tailText
            new_split_result = TextProcessor.split_text_by_comma(new_text)
            new_blocks = new_split_result.blocks
            tail_text = new_split_result.tail_text

            # 3. 根据方法匹配出文本块的不同，得到不同的文本块列表diffPhraseList
            diff_phrase_list = TextProcessor.find_different_blocks(new_blocks, old_blocks)

            # 如果没有差异，返回当前的TextInfo对象
            if not diff_phrase_list:
                current_text_info = {
                    "id": old_text_info.id,
                    "color": old_text_info.color,
                    "text": old_text_info.text
                }
                return {
                    "message": "没有新增词汇",
                    "text_info": current_text_info
                }

            # 获取diffPhraseList中不同文本块在newText位置索引
            block_index_map = TextProcessor.get_block_index_map(new_blocks, diff_phrase_list)

            # 4. 取出整个词汇表的所有词汇allPhraseList
            all_phrases = self._db.query(Phrase).all()

            # 5. 创建一个新的文本块列表副本，用于修改
            modified_blocks = new_blocks.copy()
            new_phrases = []

            # 6. 遍历不同的文本块map
            for key, index in block_index_map.items():
                # 提取实际的word（支持多种格式）
                word = self._extract_word_from_block(key)

                # 计算allPhraseList的word中与内容word相同的数量count
                count = sum(1 for phrase in all_phrases if phrase.word == word)

                # 新增一个Phrase对象
                phrase = Phrase(
                    id=generate_id(),
                    text_id=text_info_id,
                    word=word,
                    type=count
                )
                new_phrases.append(phrase)

                # 根据索引取出newText对应的词汇做字符串的拼接
                if index < len(modified_blocks):
                    original_block = modified_blocks[index]
                    # 只有当count大于0时才拼接数字
                    if count > 0:
                        modified_blocks[index] = f"{original_block}{count}"

            # 7. 批量插入新词汇
            if new_phrases:
                self._save_phrases_batch(new_phrases)

            # 8. 构建最终文本
            final_text_parts = []
            if modified_blocks:
                final_text_parts.extend(modified_blocks)
                final_text_parts.append("、")
            final_text_parts.append(tail_text)

            final_text = "、".join(modified_blocks) + "、" + tail_text if modified_blocks else tail_text

            # 9. 更新数据库
            old_text_info.text = final_text
            self._db.commit()

            # 10. 返回完整的TextInfo对象
            updated_text_info = {
                "id": old_text_info.id,
                "color": old_text_info.color,
                "text": old_text_info.text
            }

            logger.info(f"Added {len(new_phrases)} phrases, final text: {final_text}")
            return {
                "message": "添加成功",
                "text_info": updated_text_info
            }

        except Exception as e:
            self._db.rollback()
            logger.error(f"Error adding phrase: {str(e)}")
            raise BusinessException(f"添加词汇失败：{str(e)}")

    async def delete_phrase(self, color: int, new_text: str) -> Dict[str, Any]:
        """
        删除词汇 - 智能词汇删除与重新编号

        功能说明：
        1. 通过比较新旧文本，识别被删除的词汇
        2. 根据词汇是否有数字后缀，采用不同的删除策略
        3. 对于有数字后缀的词汇，会自动重新编号所有相关词汇

        Args:
            color: 颜色标识，用于定位TextInfo
            new_text: 前端传入的新文本内容

        Returns:
            Dict[str, Any]: 操作结果
            - 简单删除: {"message": "删除成功"}
            - 复杂删除: {"message": "删除成功", "updated_text_infos": [TextInfo列表]}
        """
        try:
            # 步骤1: 获取当前TextInfo和旧文本
            old_text_info = self._db.query(TextInfo).filter(TextInfo.color == color).first()
            if not old_text_info:
                raise BusinessException("文本信息不存在")

            old_text = old_text_info.text or ""
            text_info_id = old_text_info.id

            # 步骤2: 文本分析 - 按顿号切割新旧文本
            old_split_result = TextProcessor.split_text_by_comma(old_text)
            old_blocks = old_split_result.blocks

            new_split_result = TextProcessor.split_text_by_comma(new_text)
            new_blocks = new_split_result.blocks

            # 步骤3: 差异分析 - 找出被删除的词汇块
            deleted_blocks = TextProcessor.find_different_blocks(old_blocks, new_blocks)

            if not deleted_blocks:
                return {"message": "没有需要删除的词汇"}

            # 步骤4: 删除策略选择 - 根据是否有数字后缀选择处理方式
            deleted_block = deleted_blocks[0]  # 通常只有一个被删除的词汇
            has_digit_suffix = bool(re.search(r'\d$', deleted_block))

            if not has_digit_suffix:
                # 简单删除：无数字后缀的词汇（如"苹果"）
                self._handle_delete_without_digit(deleted_block, text_info_id, new_text)
                self._db.commit()
                return {"message": "删除成功"}
            else:
                # 复杂删除：有数字后缀的词汇（如"苹果1"），需要重新编号
                updated_text_infos = self._handle_delete_with_digit(deleted_block, text_info_id, new_text)
                self._db.commit()
                return {
                    "message": "删除成功",
                    "updated_text_infos": updated_text_infos
                }

        except Exception as e:
            self._db.rollback()
            logger.error(f"Error deleting phrase: {str(e)}")
            raise BusinessException(f"删除失败：{str(e)}")

    def _handle_delete_without_digit(self, deleted_block: str, text_info_id: int, new_text: str) -> None:
        """
        处理无数字后缀的词汇删除（简单删除）

        适用场景：删除如"苹果"这样没有数字后缀的词汇
        处理逻辑：
        1. 直接删除对应的Phrase记录（type=0）
        2. 使用前端传入的new_text直接更新TextInfo

        Args:
            deleted_block: 被删除的词汇块（如"苹果"）
            text_info_id: 当前TextInfo的ID
            new_text: 前端传入的新文本内容
        """
        # 删除对应的phrase记录（type=0表示无数字后缀）
        self._delete_phrase_by_word_and_type(deleted_block, 0, text_info_id)

        # 直接使用前端传入的new_text更新当前TextInfo
        self._db.query(TextInfo).filter(TextInfo.id == text_info_id).update({"text": new_text})
        self._db.flush()  # 确保更新立即生效

    def _handle_delete_with_digit(self, deleted_block: str, text_info_id: int, new_text: str) -> List[Dict[str, Any]]:
        """
        处理有数字后缀的词汇删除（复杂删除+重新编号）

        适用场景：删除如"苹果1"这样有数字后缀的词汇
        处理逻辑：
        1. 解析词汇和数字后缀
        2. 删除目标Phrase记录
        3. 立即更新当前TextInfo
        4. 查找所有需要重新编号的相关词汇（按type升序）
        5. 批量更新相关词汇的type和对应TextInfo的文本

        Args:
            deleted_block: 被删除的词汇块（如"苹果1"）
            text_info_id: 当前TextInfo的ID
            new_text: 前端传入的新文本内容

        Returns:
            List[Dict[str, Any]]: 所有被更新的TextInfo对象列表
        """
        # 步骤1: 解析词汇和数字后缀
        match = re.search(r'^(.+?)(\d+)$', deleted_block)
        if match:
            word = match.group(1)  # 提取词汇部分（如"苹果"）
            type_value = int(match.group(2))  # 提取数字部分（如1）

            # 步骤2: 删除目标Phrase记录
            self._delete_phrase_by_word_and_type(word, type_value, text_info_id)

            # 步骤3: 立即更新当前TextInfo（使用前端传入的new_text）
            self._db.query(TextInfo).filter(TextInfo.id == text_info_id).update({"text": new_text})
            self._db.flush()  # 确保更新立即生效

            # 步骤4: 查找需要重新编号的相关词汇（全局查询，按type升序排列）
            phrase_list_to_update = self._db.query(Phrase).filter(
                Phrase.word == word,  # 相同词汇
                Phrase.type > type_value  # type大于被删除的type
            ).order_by(Phrase.type.asc()).all()  # 按type从小到大排序，确保正确的重新编号

            # 步骤5: 批量重新编号处理
            if phrase_list_to_update:
                # 5.1 获取所有相关的TextInfo对象
                text_ids = list(set([phrase.text_id for phrase in phrase_list_to_update]))
                text_infos = self._db.query(TextInfo).filter(TextInfo.id.in_(text_ids)).all()
                text_info_dict = {text_info.id: text_info for text_info in text_infos}

                # 5.2 遍历需要更新的phrase（已按type从小到大排序）
                for phrase in phrase_list_to_update:
                    # 构建旧的词汇文本（如"苹果2"）
                    original_type = phrase.type
                    old_word = phrase.word + str(original_type)

                    # 计算新的type值（减1，因为删除了一个更小的type）
                    new_type = original_type - 1
                    new_word = phrase.word + (str(new_type) if new_type > 0 else "")

                    # 在对应的TextInfo中更新词汇文本
                    text_info = text_info_dict.get(phrase.text_id)
                    if text_info:
                        # 替换文本中的词汇（如"苹果2" -> "苹果1"）
                        current_text = text_info.text or ""
                        new_word_text = current_text.replace(old_word, new_word)
                        text_info.text = new_word_text

                    # 更新phrase的type值
                    phrase.type = new_type

                # 5.3 批量提交数据库更新
                self._update_phrases_batch(phrase_list_to_update)  # 更新Phrase表

                self._db.bulk_update_mappings(TextInfo, [  # 批量更新TextInfo表
                    {"id": text_info.id, "text": text_info.text}
                    for text_info in text_infos
                ])

                # 返回所有被更新的TextInfo对象（包括当前的和相关的）
                all_updated_text_infos = []

                # 添加当前更新的TextInfo
                current_text_info = self._db.query(TextInfo).filter(TextInfo.id == text_info_id).first()
                if current_text_info:
                    all_updated_text_infos.append({
                        "id": current_text_info.id,
                        "color": current_text_info.color,
                        "text": current_text_info.text
                    })

                # 添加相关更新的TextInfo
                for text_info in text_infos:
                    all_updated_text_infos.append({
                        "id": text_info.id,
                        "color": text_info.color,
                        "text": text_info.text
                    })

                return all_updated_text_infos
            else:
                # 没有相关词汇需要更新，只返回当前的TextInfo
                current_text_info = self._db.query(TextInfo).filter(TextInfo.id == text_info_id).first()
                if current_text_info:   
                    return [{
                        "id": current_text_info.id,
                        "color": current_text_info.color,
                        "text": current_text_info.text
                    }]
                return []

    async def get_phrases_by_color(self, color: int) -> List[PhraseResponse]:
        """根据颜色查询词汇列表"""
        try:
            # 通过color获取TextInfo
            text_info = self._db.query(TextInfo).filter(TextInfo.color == color).first()
            if not text_info:
                return []

            # 查询该TextInfo下的所有Phrase
            phrases = self._db.query(Phrase).filter(Phrase.text_id == text_info.id).all()

            # 转换为PhraseResponse格式
            result = []
            for phrase in phrases:
                result.append(PhraseResponse(
                    id=phrase.id,
                    text_id=phrase.text_id,
                    word=phrase.word,
                    phrase_type=phrase.type
                ))

            return result
        except Exception as e:
            raise BusinessException(f"查询词汇失败: {str(e)}")
