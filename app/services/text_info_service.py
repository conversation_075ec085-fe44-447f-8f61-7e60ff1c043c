from sqlalchemy.orm import Session
from app.models.text_info import TextInfo
from app.utils.id_generator import generate_id
from app.utils.exceptions import BusinessException
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)

class TextInfoService:
    """文本信息服务类 - 独立的文本信息管理"""

    def __init__(self, db: Session):
        self._db = db

    async def get_all_text_infos(self) -> List[TextInfo]:
        """查询所有TextInfo列表"""
        try:
            text_infos = self._db.query(TextInfo).order_by(TextInfo.color).all()
            logger.debug(f"Found {len(text_infos)} text_infos")
            return text_infos
        except Exception as e:
            raise BusinessException(f"查询文本信息失败: {str(e)}")

    async def get_text_info_by_id(self, text_info_id: int) -> Optional[TextInfo]:
        """根据ID获取TextInfo"""
        try:
            return self._db.query(TextInfo).filter(TextInfo.id == text_info_id).first()
        except Exception as e:
            raise BusinessException(f"查询文本信息失败: {str(e)}")

    async def update_text_info_by_data(self, text_info_data: dict) -> TextInfo:
        """根据传入的数据更新TextInfo"""
        try:
            # 根据ID查找现有记录
            existing_text_info = await self.get_text_info_by_id(text_info_data["id"])
            if not existing_text_info:
                raise BusinessException("文本信息不存在")

            # 更新字段
            existing_text_info.color = text_info_data["color"]
            existing_text_info.text = text_info_data["text"]

            # 保存到数据库
            self._db.merge(existing_text_info)
            self._db.commit()

            return existing_text_info
        except Exception as e:
            self._db.rollback()
            raise BusinessException(f"更新文本信息失败: {str(e)}")

