from pydantic import BaseModel, Field, ConfigDict, field_serializer
from typing import List

class PhraseBase(BaseModel):
    word: str = Field(..., min_length=1, max_length=255, description="词汇内容")
    phrase_type: int = Field(..., ge=0, description="词汇类型", alias="type")

class PhraseCreate(PhraseBase):
    """创建词汇的请求模型"""
    text_id: int = Field(..., gt=0, description="关联文本ID")

class PhraseUpdate(BaseModel):
    """更新词汇的请求模型"""
    id: int = Field(..., gt=0, description="词汇ID")
    text_id: int = Field(..., gt=0, description="关联文本ID")
    word: str = Field(..., min_length=1, max_length=255, description="词汇内容")
    phrase_type: int = Field(..., ge=0, description="词汇类型", alias="type")

class PhraseResponse(BaseModel):
    """词汇响应模型"""
    id: int
    text_id: int
    word: str
    phrase_type: int = Field(..., alias="type")

    @field_serializer('id', 'text_id')
    def serialize_ids(self, value: int) -> str:
        """将ID序列化为字符串，避免JavaScript精度丢失"""
        return str(value)

    model_config = ConfigDict(from_attributes=True, populate_by_name=True)

class PhraseListResponse(BaseModel):
    """词汇列表响应模型"""
    phrases: List[PhraseResponse]
    total: int
