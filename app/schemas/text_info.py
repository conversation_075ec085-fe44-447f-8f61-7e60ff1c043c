from pydantic import BaseModel, Field, ConfigDict, field_serializer
from typing import Optional

class TextInfoBase(BaseModel):
    color: int = Field(..., ge=0, le=8, description="颜色标识，0-8")
    text: Optional[str] = Field("", max_length=1000, description="文本内容")

class TextInfoCreate(TextInfoBase):
    """创建文本信息的请求模型"""
    pass

class TextInfoUpdate(BaseModel):
    """更新文本信息的请求模型"""
    id: int = Field(..., description="文本信息ID")
    color: int = Field(..., ge=0, le=8, description="颜色标识，0-8")
    text: str = Field(..., max_length=1000, description="文本内容")

class TextInfoColorUpdate(BaseModel):
    """词汇操作的请求模型（只需要color和text）"""
    color: int = Field(..., ge=0, le=8, description="颜色标识，0-8")
    text: str = Field(..., max_length=1000, description="文本内容")

class TextInfoResponse(TextInfoBase):
    """文本信息响应模型"""
    id: int

    @field_serializer('id')
    def serialize_id(self, value: int) -> str:
        """将ID序列化为字符串，避免JavaScript精度丢失"""
        return str(value)

    model_config = ConfigDict(from_attributes=True)
