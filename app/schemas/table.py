from pydantic import BaseModel, Field, ConfigDict, field_serializer
from datetime import datetime
from typing import List

class TableBase(BaseModel):
    """表格基础模型"""
    name: str = Field(..., min_length=1, max_length=255, description="表格名称")

class TableCreate(TableBase):
    """创建表格的请求模型"""
    pass

class TableUpdate(BaseModel):
    """更新表格的请求模型"""
    id: int = Field(..., gt=0, description="表格ID")
    name: str = Field(..., min_length=1, max_length=255, description="表格名称")

class TableResponse(TableBase):
    """表格响应模型"""
    id: int
    create_time: datetime  # 保持与数据库字段一致

    @field_serializer('id')
    def serialize_id(self, value: int) -> str:
        """将ID序列化为字符串，避免JavaScript精度丢失"""
        return str(value)

    model_config = ConfigDict(from_attributes=True)

class TableListResponse(BaseModel):
    """表格列表响应模型"""
    tables: List[TableResponse]
    total: int = Field(..., ge=0, description="总数量")
