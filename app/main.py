from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
from app.config.database import engine, Base
from app.routers import table, text_info, phrase, coordinate
from app.utils.exceptions import BusinessException

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时创建数据库表
    logger.info("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created successfully")
    yield
    # 关闭时的清理工作
    logger.info("Application shutdown")

# 创建FastAPI应用
app = FastAPI(
    title="Cube Text Processing API",
    description="文本处理和坐标管理系统",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理
@app.exception_handler(BusinessException)
async def business_exception_handler(request, exc: BusinessException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )

# 注册路由
app.include_router(table.router)
app.include_router(text_info.router)
app.include_router(phrase.router)
app.include_router(coordinate.router)
    
@app.get("/", tags=["health"])
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "message": "Cube API is running"}

@app.get("/health", tags=["health"])
async def detailed_health_check():
    """详细健康检查"""
    return {
        "status": "healthy",
        "service": "Cube Text Processing API",
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8080,
        reload=True,
        log_level="info"
    )
